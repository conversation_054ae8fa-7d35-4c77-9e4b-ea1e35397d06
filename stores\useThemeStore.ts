import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import colors from "@/constants/colors";

// Define theme types
export interface Theme {
  primary: string;
  primaryLight: string;
  secondary: string;
  secondaryLight: string;
  text: string;
  textSecondary: string;
  background: string;
  card: string;
  border: string;
}

// Create light and dark themes
const lightTheme: Theme = {
  primary: colors.primary,
  primaryLight: colors.primaryLight,
  secondary: colors.secondary,
  secondaryLight: colors.secondaryLight,
  text: colors.text,
  textSecondary: colors.textSecondary,
  background: colors.background,
  card: colors.card,
  border: colors.border,
};

const darkTheme: Theme = {
  primary: colors.primary,
  primaryLight: "#1F2937", // Darker background for primary elements
  secondary: colors.secondary,
  secondaryLight: "#1E1B4B", // Darker background for secondary elements
  text: "#F9FAFB", // Light text for dark mode
  textSecondary: "#D1D5DB", // Light secondary text for dark mode
  background: "#111827", // Dark background
  card: "#1F2937", // Dark card background
  border: "#374151", // Dark border
};

// Define the theme state
export interface ThemeState {
  isDarkMode: boolean;
  theme: Theme;
  toggleDarkMode: () => void;
}

// Create the theme store
export const useThemeStore = create<ThemeState>()(
  persist(
    (set) => ({
      isDarkMode: false,
      theme: lightTheme,
      toggleDarkMode: () => 
        set((state) => ({
          isDarkMode: !state.isDarkMode,
          theme: state.isDarkMode ? lightTheme : darkTheme,
        })),
    }),
    {
      name: "theme-storage",
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);