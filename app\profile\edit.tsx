import React, { useState } from "react";
import { 
  StyleSheet, 
  Text, 
  View, 
  ScrollView, 
  TextInput, 
  TouchableOpacity, 
  Image, 
  Alert, 
  Modal,
  FlatList,
  Platform,
  ActivityIndicator
} from "react-native";
import { Stack, useRouter } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import Button from "@/components/Button";
import { useAuthStore } from "@/stores/useAuthStore";
import { Camera, User, MapPin, X, Upload, Image as ImageIcon } from "lucide-react-native";
import { useTranslation } from "@/hooks/useTranslation";
import * as ImagePicker from "expo-image-picker";
import * as FileSystem from "expo-file-system";

// Sample profile pictures
const profilePictures = [
  "https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80",
  "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80",
  "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80",
  "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80",
  "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80",
  "https://images.unsplash.com/photo-1607746882042-944635dfe10e?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80",
];

// Available locations
const locations = [
  { key: "location.capeTown", value: "location.capeTown" },
  { key: "location.johannesburg", value: "location.johannesburg" },
  { key: "location.durban", value: "location.durban" },
  { key: "location.pretoria", value: "location.pretoria" },
];

export default function EditProfileScreen() {
  const router = useRouter();
  const { user, updateProfile } = useAuthStore();
  const { t } = useTranslation();
  
  const [name, setName] = useState(user?.name || "");
  const [email, setEmail] = useState(user?.email || "");
  const [phone, setPhone] = useState(user?.phone || "");
  const [location, setLocation] = useState(user?.location || "location.capeTown");
  const [isLoading, setIsLoading] = useState(false);
  const [showPhotoModal, setShowPhotoModal] = useState(false);
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [showPhotoOptions, setShowPhotoOptions] = useState(false);
  const [processingImage, setProcessingImage] = useState(false);

  const handleSave = async () => {
    if (!name.trim()) {
      Alert.alert(t("error"), t("nameRequired"));
      return;
    }

    setIsLoading(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      updateProfile({
        name,
        email,
        phone,
        location,
      });

      Alert.alert(t("success"), t("profileUpdated"));
      router.back();
    } catch (error) {
      Alert.alert(t("error"), t("updateFailed"));
    } finally {
      setIsLoading(false);
    }
  };

  const handleChangePhoto = () => {
    setShowPhotoOptions(true);
  };

  const handleSelectPhoto = (photoUrl: string) => {
    updateProfile({
      avatar: photoUrl,
    });
    setShowPhotoModal(false);
  };

  const handleSelectLocation = (locationKey: string) => {
    setLocation(locationKey);
    setShowLocationModal(false);
  };

  // Function to resize and optimize image
  const optimizeImage = async (uri: string): Promise<string> => {
    try {
      // Get file info
      const fileInfo = await FileSystem.getInfoAsync(uri);
      
      // Check if file exists and has size property
      if (fileInfo.exists && 'size' in fileInfo && fileInfo.size > 1024 * 1024) {
        console.log('Image is large, would resize in production app');
        // In a real app: return await ImageManipulator.manipulateAsync(uri, [{resize: {width: 500}}], {compress: 0.7});
      }
      
      return uri;
    } catch (error) {
      console.error('Error optimizing image:', error);
      return uri;
    }
  };

  const pickImage = async () => {
    setShowPhotoOptions(false);
    setProcessingImage(true);
    
    try {
      // Request permission
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== "granted") {
        Alert.alert(t("error"), t("cameraPermissionDenied"));
        setProcessingImage(false);
        return;
      }
      
      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.7,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        // Optimize image before updating profile
        const optimizedUri = await optimizeImage(result.assets[0].uri);
        
        // Update profile with selected image
        updateProfile({
          avatar: optimizedUri,
        });
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert(t("error"), t("updateFailed"));
    } finally {
      setProcessingImage(false);
    }
  };

  const takePhoto = async () => {
    setShowPhotoOptions(false);
    setProcessingImage(true);
    
    try {
      // Request camera permission
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      
      if (status !== "granted") {
        Alert.alert(t("error"), t("cameraPermissionDenied"));
        setProcessingImage(false);
        return;
      }
      
      // Launch camera
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.7,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        // Optimize image before updating profile
        const optimizedUri = await optimizeImage(result.assets[0].uri);
        
        // Update profile with captured image
        updateProfile({
          avatar: optimizedUri,
        });
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert(t("error"), t("updateFailed"));
    } finally {
      setProcessingImage(false);
    }
  };

  const chooseFromGallery = () => {
    setShowPhotoOptions(false);
    setShowPhotoModal(true);
  };

  return (
    <>
      <Stack.Screen options={{ title: t("editProfile") }} />
      
      <ScrollView style={styles.container}>
        <View style={styles.photoSection}>
          <View style={styles.avatarContainer}>
            {processingImage ? (
              <View style={styles.placeholderAvatar}>
                <ActivityIndicator size="large" color={colors.white} />
              </View>
            ) : user?.avatar ? (
              <Image
                source={{ uri: user.avatar }}
                style={styles.avatar}
              />
            ) : (
              <View style={styles.placeholderAvatar}>
                <User size={40} color={colors.white} />
              </View>
            )}
            <TouchableOpacity
              style={styles.cameraButton}
              onPress={handleChangePhoto}
              disabled={processingImage}
            >
              <Camera size={20} color={colors.white} />
            </TouchableOpacity>
          </View>
          <TouchableOpacity onPress={handleChangePhoto} disabled={processingImage}>
            <Text style={styles.changePhotoText}>{t("changePhoto")}</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.formSection}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t("fullName")}</Text>
            <TextInput
              style={styles.input}
              value={name}
              onChangeText={setName}
              placeholder={t("enterFullName")}
              placeholderTextColor={colors.textSecondary}
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t("email")}</Text>
            <TextInput
              style={styles.input}
              value={email}
              onChangeText={setEmail}
              placeholder={t("enterEmail")}
              placeholderTextColor={colors.textSecondary}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t("phoneNumber")}</Text>
            <TextInput
              style={styles.input}
              value={phone}
              onChangeText={setPhone}
              placeholder={t("enterPhoneNumber")}
              placeholderTextColor={colors.textSecondary}
              keyboardType="phone-pad"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t("location")}</Text>
            <TouchableOpacity 
              style={styles.locationSelector}
              onPress={() => setShowLocationModal(true)}
            >
              <MapPin size={20} color={colors.primary} />
              <Text style={styles.locationText}>{t(location)}</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        <Button
          title={t("saveChanges")}
          onPress={handleSave}
          loading={isLoading}
          disabled={isLoading || processingImage}
          style={styles.saveButton}
        />
      </ScrollView>

      {/* Photo Options Modal */}
      <Modal
        visible={showPhotoOptions}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowPhotoOptions(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t("changePhoto")}</Text>
              <TouchableOpacity 
                style={styles.closeButton}
                onPress={() => setShowPhotoOptions(false)}
              >
                <X size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.photoOptionsContainer}>
              {Platform.OS !== "web" && (
                <TouchableOpacity 
                  style={styles.photoOptionItem}
                  onPress={takePhoto}
                >
                  <View style={styles.photoOptionIcon}>
                    <Camera size={24} color={colors.primary} />
                  </View>
                  <Text style={styles.photoOptionText}>{t("takePhoto")}</Text>
                </TouchableOpacity>
              )}
              
              <TouchableOpacity 
                style={styles.photoOptionItem}
                onPress={pickImage}
              >
                <View style={styles.photoOptionIcon}>
                  <Upload size={24} color={colors.primary} />
                </View>
                <Text style={styles.photoOptionText}>{t("uploadPhoto")}</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.photoOptionItem}
                onPress={chooseFromGallery}
              >
                <View style={styles.photoOptionIcon}>
                  <ImageIcon size={24} color={colors.primary} />
                </View>
                <Text style={styles.photoOptionText}>{t("chooseFromGallery")}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Profile Picture Modal */}
      <Modal
        visible={showPhotoModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowPhotoModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t("selectProfilePicture")}</Text>
              <TouchableOpacity 
                style={styles.closeButton}
                onPress={() => setShowPhotoModal(false)}
              >
                <X size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
            
            <FlatList
              data={profilePictures}
              numColumns={3}
              keyExtractor={(item) => item}
              renderItem={({ item }) => (
                <TouchableOpacity 
                  style={styles.photoItem}
                  onPress={() => handleSelectPhoto(item)}
                >
                  <Image source={{ uri: item }} style={styles.photoThumbnail} />
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.photoGrid}
            />
          </View>
        </View>
      </Modal>

      {/* Location Modal */}
      <Modal
        visible={showLocationModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowLocationModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t("selectLocation")}</Text>
              <TouchableOpacity 
                style={styles.closeButton}
                onPress={() => setShowLocationModal(false)}
              >
                <X size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
            
            {locations.map((loc) => (
              <TouchableOpacity 
                key={loc.key}
                style={[
                  styles.locationItem,
                  location === loc.value && styles.selectedLocationItem
                ]}
                onPress={() => handleSelectLocation(loc.value)}
              >
                <Text 
                  style={[
                    styles.locationItemText,
                    location === loc.value && styles.selectedLocationItemText
                  ]}
                >
                  {t(loc.value)}
                </Text>
                {location === loc.value && (
                  <View style={styles.selectedIndicator} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  photoSection: {
    alignItems: "center",
    padding: 24,
    backgroundColor: colors.white,
  },
  avatarContainer: {
    position: "relative",
    marginBottom: 8,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  placeholderAvatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
  },
  cameraButton: {
    position: "absolute",
    bottom: 0,
    right: 0,
    backgroundColor: colors.primary,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 3,
    borderColor: colors.white,
  },
  changePhotoText: {
    ...typography.body,
    color: colors.primary,
  },
  formSection: {
    backgroundColor: colors.white,
    marginTop: 16,
    padding: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    ...typography.bodySmall,
    fontWeight: "600",
    marginBottom: 8,
  },
  input: {
    ...typography.body,
    backgroundColor: colors.card,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  locationSelector: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.card,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: colors.border,
    gap: 8,
  },
  locationText: {
    ...typography.body,
    flex: 1,
  },
  saveButton: {
    margin: 16,
  },
  modalContainer: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingBottom: 24,
    maxHeight: "80%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    ...typography.h3,
  },
  closeButton: {
    padding: 4,
  },
  photoGrid: {
    padding: 8,
  },
  photoItem: {
    flex: 1,
    margin: 8,
    aspectRatio: 1,
    maxWidth: "30%",
  },
  photoThumbnail: {
    width: "100%",
    height: "100%",
    borderRadius: 8,
  },
  locationItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  selectedLocationItem: {
    backgroundColor: colors.primaryLight,
  },
  locationItemText: {
    ...typography.body,
  },
  selectedLocationItemText: {
    color: colors.primary,
    fontWeight: "600",
  },
  selectedIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.primary,
  },
  photoOptionsContainer: {
    padding: 16,
  },
  photoOptionItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  photoOptionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primaryLight,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  photoOptionText: {
    ...typography.body,
  },
});