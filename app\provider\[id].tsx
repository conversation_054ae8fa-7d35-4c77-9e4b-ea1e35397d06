import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, Image, TouchableOpacity } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { serviceProviders } from "@/mocks/providers";
import { reviews } from "@/mocks/reviews";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import ServiceCard from "@/components/ServiceCard";
import ReviewCard from "@/components/ReviewCard";
import { Star, MapPin, Calendar, Clock, ChevronLeft, Heart } from "lucide-react-native";
import { useTranslation } from "@/hooks/useTranslation";
import { useBookingStore } from "@/stores/useBookingStore";

export default function ProviderScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const { t } = useTranslation();
  const [isSaved, setIsSaved] = useState(false);
  const { setCurrentBookingProvider, setCurrentBookingService, clearCurrentBooking } = useBookingStore();

  // Find the provider by ID
  const provider = serviceProviders.find((p) => p.id === id);

  // Get reviews for this provider
  const providerReviews = reviews.filter((r) => r.providerId === id);

  // Clear any existing booking data when viewing a new provider
  useEffect(() => {
    clearCurrentBooking();
  }, [id, clearCurrentBooking]);

  if (!provider) {
    return (
      <View style={styles.notFoundContainer}>
        <Text style={styles.notFoundText}>{t("providerNotFound")}</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>{t("goBack")}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const handleServicePress = (service: any) => {
    // Set provider and service in the booking store
    setCurrentBookingProvider(provider.id, t(provider.nameKey));
    setCurrentBookingService(service);
    
    // Navigate to booking confirmation
    router.push({
      pathname: "/booking/confirm",
      params: { providerId: provider.id, serviceId: service.id },
    });
  };

  const toggleSaved = () => {
    setIsSaved(!isSaved);
  };

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Image source={{ uri: provider.avatar }} style={styles.avatar} />
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ChevronLeft size={24} color={colors.white} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.saveButton} onPress={toggleSaved}>
            <Heart
              size={24}
              color={colors.white}
              fill={isSaved ? colors.error : "none"}
            />
          </TouchableOpacity>
        </View>

        <View style={styles.content}>
          <View style={styles.providerInfo}>
            <Text style={styles.name}>{t(provider.nameKey)}</Text>
            <View style={styles.ratingContainer}>
              <Star
                size={18}
                color={colors.secondary}
                fill={colors.secondary}
                strokeWidth={2}
              />
              <Text style={styles.rating}>
                {provider.rating} ({provider.reviewCount} {t("reviews")})
              </Text>
            </View>
            <View style={styles.locationContainer}>
              <MapPin size={16} color={colors.textSecondary} strokeWidth={2} />
              <Text style={styles.location}>
                {t(provider.locationKey)} • {provider.distance} km
              </Text>
            </View>
          </View>

          <Text style={styles.description}>{t(provider.descriptionKey)}</Text>

          <View style={styles.availabilityContainer}>
            <View style={styles.availabilityHeader}>
              <Calendar size={18} color={colors.primary} strokeWidth={2} />
              <Text style={styles.availabilityTitle}>{t("availability")}</Text>
            </View>
            <View style={styles.availabilityList}>
              {provider.availabilityKeys.map((availabilityKey, index) => (
                <View key={index} style={styles.availabilityItem}>
                  <Clock size={14} color={colors.textSecondary} strokeWidth={2} />
                  <Text style={styles.availabilityText}>
                    {t(availabilityKey)}
                  </Text>
                </View>
              ))}
            </View>
          </View>

          <View style={styles.servicesSection}>
            <Text style={styles.sectionTitle}>{t("services")}</Text>
            {provider.services.map((service) => (
              <ServiceCard
                key={service.id}
                service={service}
                onPress={handleServicePress}
              />
            ))}
          </View>

          <View style={styles.reviewsSection}>
            <Text style={styles.sectionTitle}>
              {t("reviews")} ({providerReviews.length})
            </Text>
            {providerReviews.map((review) => (
              <ReviewCard key={review.id} review={review} />
            ))}
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.bookButton}
          onPress={() => {
            if (provider.services.length > 0) {
              handleServicePress(provider.services[0]);
            }
          }}
        >
          <Text style={styles.bookButtonText}>{t("bookNow")}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    position: "relative",
    height: 250,
  },
  avatar: {
    width: "100%",
    height: "100%",
  },
  backButton: {
    position: "absolute",
    top: 16,
    left: 16,
    backgroundColor: "rgba(0, 0, 0, 0.3)",
    borderRadius: 20,
    padding: 8,
  },
  saveButton: {
    position: "absolute",
    top: 16,
    right: 16,
    backgroundColor: "rgba(0, 0, 0, 0.3)",
    borderRadius: 20,
    padding: 8,
  },
  content: {
    padding: 16,
  },
  providerInfo: {
    marginBottom: 16,
  },
  name: {
    ...typography.h1,
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
    gap: 4,
  },
  rating: {
    ...typography.body,
    fontWeight: "500",
  },
  locationContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  location: {
    ...typography.body,
    color: colors.textSecondary,
  },
  description: {
    ...typography.body,
    marginBottom: 24,
  },
  availabilityContainer: {
    marginBottom: 24,
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
  },
  availabilityHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
    gap: 8,
  },
  availabilityTitle: {
    ...typography.h3,
  },
  availabilityList: {
    gap: 8,
  },
  availabilityItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  availabilityText: {
    ...typography.body,
  },
  servicesSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    ...typography.h2,
    marginBottom: 16,
  },
  reviewsSection: {
    marginBottom: 24,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    backgroundColor: colors.white,
  },
  bookButton: {
    backgroundColor: colors.primary,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  bookButtonText: {
    ...typography.button,
    color: colors.white,
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  notFoundText: {
    ...typography.h2,
    marginBottom: 16,
    textAlign: "center",
  },
  backButtonText: {
    ...typography.button,
    color: colors.primary,
  },
});