export interface ServiceProvider {
  id: string;
  nameKey: string; // Translation key for name
  avatar: string;
  categoryId: string;
  services: Service[];
  rating: number;
  reviewCount: number;
  locationKey: string; // Translation key for location
  distance: number; // in km
  descriptionKey: string; // Translation key for description
  verified: boolean;
  featured: boolean;
  availabilityKeys: string[]; // Translation keys for availability
}

export interface Service {
  id: string;
  nameKey: string; // Translation key for name
  price: number;
  duration: number; // in minutes
  descriptionKey: string; // Translation key for description
}

export const serviceProviders: ServiceProvider[] = [
  {
    id: "sp1",
    nameKey: "provider.thaboCleaning",
    avatar: "https://images.unsplash.com/photo-1566753323558-f4e0952af115?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80",
    categoryId: "home-services",
    services: [
      {
        id: "s1",
        nameKey: "service.standardHomeCleaning",
        price: 350,
        duration: 180,
        descriptionKey: "serviceDesc.standardHomeCleaning"
      },
      {
        id: "s2",
        nameKey: "service.deepCleaning",
        price: 650,
        duration: 300,
        descriptionKey: "serviceDesc.deepCleaning"
      }
    ],
    rating: 4.8,
    reviewCount: 124,
    locationKey: "location.capeTown",
    distance: 3.2,
    descriptionKey: "providerDesc.thaboCleaning",
    verified: true,
    featured: true,
    availabilityKeys: ["availability.weekdays", "availability.saturday"]
  },
  {
    id: "sp2",
    nameKey: "provider.nomsaGarden",
    avatar: "https://images.unsplash.com/photo-1621905252507-b35492cc74b4?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80",
    categoryId: "home-services",
    services: [
      {
        id: "s3",
        nameKey: "service.gardenMaintenance",
        price: 280,
        duration: 120,
        descriptionKey: "serviceDesc.gardenMaintenance"
      },
      {
        id: "s4",
        nameKey: "service.landscapeDesign",
        price: 1200,
        duration: 480,
        descriptionKey: "serviceDesc.landscapeDesign"
      }
    ],
    rating: 4.6,
    reviewCount: 87,
    locationKey: "location.johannesburg",
    distance: 5.7,
    descriptionKey: "providerDesc.nomsaGarden",
    verified: true,
    featured: false,
    availabilityKeys: ["availability.mondayToSaturday"]
  },
  {
    id: "sp3",
    nameKey: "provider.siphoElectrical",
    avatar: "https://images.unsplash.com/photo-1621905251189-08b45d6a269e?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80",
    categoryId: "home-services",
    services: [
      {
        id: "s5",
        nameKey: "service.electricalRepairs",
        price: 450,
        duration: 120,
        descriptionKey: "serviceDesc.electricalRepairs"
      },
      {
        id: "s6",
        nameKey: "service.newInstallation",
        price: 850,
        duration: 240,
        descriptionKey: "serviceDesc.newInstallation"
      }
    ],
    rating: 4.9,
    reviewCount: 156,
    locationKey: "location.durban",
    distance: 4.1,
    descriptionKey: "providerDesc.siphoElectrical",
    verified: true,
    featured: true,
    availabilityKeys: ["availability.allWeek"]
  },
  {
    id: "sp4",
    nameKey: "provider.leratoBeauty",
    avatar: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80",
    categoryId: "personal-care",
    services: [
      {
        id: "s7",
        nameKey: "service.haircutStyling",
        price: 280,
        duration: 90,
        descriptionKey: "serviceDesc.haircutStyling"
      },
      {
        id: "s8",
        nameKey: "service.makeupApplication",
        price: 350,
        duration: 60,
        descriptionKey: "serviceDesc.makeupApplication"
      }
    ],
    rating: 4.7,
    reviewCount: 92,
    locationKey: "location.pretoria",
    distance: 2.8,
    descriptionKey: "providerDesc.leratoBeauty",
    verified: true,
    featured: false,
    availabilityKeys: ["availability.tuesdayToSaturday"]
  },
  {
    id: "sp5",
    nameKey: "provider.bonganiTech",
    avatar: "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80",
    categoryId: "tech-support",
    services: [
      {
        id: "s9",
        nameKey: "service.computerRepair",
        price: 400,
        duration: 120,
        descriptionKey: "serviceDesc.computerRepair"
      },
      {
        id: "s10",
        nameKey: "service.networkSetup",
        price: 650,
        duration: 180,
        descriptionKey: "serviceDesc.networkSetup"
      }
    ],
    rating: 4.8,
    reviewCount: 113,
    locationKey: "location.capeTown",
    distance: 6.3,
    descriptionKey: "providerDesc.bonganiTech",
    verified: true,
    featured: true,
    availabilityKeys: ["availability.weekdays", "availability.saturdayShort"]
  },
  {
    id: "sp6",
    nameKey: "provider.ayandaTutoring",
    avatar: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80",
    categoryId: "education",
    services: [
      {
        id: "s11",
        nameKey: "service.mathTutoring",
        price: 250,
        duration: 60,
        descriptionKey: "serviceDesc.mathTutoring"
      },
      {
        id: "s12",
        nameKey: "service.scienceTutoring",
        price: 250,
        duration: 60,
        descriptionKey: "serviceDesc.scienceTutoring"
      }
    ],
    rating: 4.9,
    reviewCount: 78,
    locationKey: "location.johannesburg",
    distance: 4.5,
    descriptionKey: "providerDesc.ayandaTutoring",
    verified: true,
    featured: false,
    availabilityKeys: ["availability.weekdaysEvening", "availability.saturdayMorning"]
  }
];