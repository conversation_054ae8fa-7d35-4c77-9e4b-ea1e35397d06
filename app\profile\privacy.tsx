import React from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Image } from "react-native";
import { Stack, useRouter } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { Plus, CreditCard, CheckCircle, Trash2 } from "lucide-react-native";

import { useTranslation } from "@/hooks/useTranslation";

// Mock payment methods
const paymentMethods = [
  {
    id: "1",
    type: "visa",
    last4: "4242",
    expMonth: 12,
    expYear: 2024,
    isDefault: true,
  },
  {
    id: "2",
    type: "mastercard",
    last4: "5555",
    expMonth: 8,
    expYear: 2025,
    isDefault: false,
  },
];

export default function PaymentMethodsScreen() {
  const router = useRouter();
  const { t } = useTranslation();

  const handleAddPaymentMethod = () => {
    router.push("/profile/add-payment");
  };

  const handleSetDefault = (id: string) => {
    // In a real app, this would update the default payment method
    alert(t("paymentMethodSetAsDefault"));
  };

  const handleRemove = (id: string) => {
    // In a real app, this would show a confirmation dialog and then remove the payment method
    alert(t("paymentMethodRemoved"));
  };

  const getCardImage = (type: string) => {
    switch (type) {
      case "visa":
        return "https://upload.wikimedia.org/wikipedia/commons/thumb/5/5e/Visa_Inc._logo.svg/2560px-Visa_Inc._logo.svg.png";
      case "mastercard":
        return "https://upload.wikimedia.org/wikipedia/commons/thumb/2/2a/Mastercard-logo.svg/1280px-Mastercard-logo.svg.png";
      default:
        return "";
    }
  };

  return (
    <>
      <Stack.Screen options={{ title: t("paymentMethods") }} />

      <View style={styles.container}>
        {paymentMethods.length > 0 ? (
          <>
            <Text style={styles.sectionTitle}>{t("savedPaymentMethods")}</Text>

            <ScrollView style={styles.paymentList}>
              {paymentMethods.map((method) => (
                <View key={method.id} style={styles.paymentCard}>
                  <View style={styles.cardHeader}>
                    <Image
                      source={{ uri: getCardImage(method.type) }}
                      style={styles.cardLogo}
                      resizeMode="contain"
                    />
                    {method.isDefault && (
                      <View style={styles.defaultBadge}>
                        <Text style={styles.defaultText}>{t("defaultPayment")}</Text>
                      </View>
                    )}
                  </View>

                  <Text style={styles.cardNumber}>•••• •••• •••• {method.last4}</Text>

                  <View style={styles.cardDetails}>
                    <Text style={styles.expiryLabel}>{t("expires")}</Text>
                    <Text style={styles.expiryDate}>
                      {method.expMonth}/{method.expYear.toString().slice(-2)}
                    </Text>
                  </View>

                  <View style={styles.cardActions}>
                    {!method.isDefault && (
                      <TouchableOpacity
                        style={styles.actionButton}
                        onPress={() => handleSetDefault(method.id)}
                      >
                        <CheckCircle size={16} color={colors.primary} />
                        <Text style={styles.actionText}>{t("setAsDefault")}</Text>
                      </TouchableOpacity>
                    )}

                    <TouchableOpacity
                      style={styles.actionButton}
                      onPress={() => handleRemove(method.id)}
                    >
                      <Trash2 size={16} color={colors.error} />
                      <Text style={[styles.actionText, { color: colors.error }]}>
                        {t("remove")}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ))}
            </ScrollView>
          </>
        ) : (
          <View style={styles.emptyState}>
            <CreditCard size={48} color={colors.textSecondary} />
            <Text style={styles.emptyTitle}>{t("noPaymentMethods")}</Text>
            <Text style={styles.emptyMessage}>
              {t("addPaymentMethodDescription")}
            </Text>
          </View>
        )}

        <TouchableOpacity
          style={[styles.addButton, styles.primaryButton]}
          onPress={handleAddPaymentMethod}
        >
          <Plus size={18} color={colors.white} />
          <Text style={styles.buttonText}>{t("addPaymentMethod")}</Text>
        </TouchableOpacity>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    padding: 16,
  },
  sectionTitle: {
    ...typography.h3,
    marginBottom: 16,
  },
  paymentList: {
    flex: 1,
  },
  paymentCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  cardLogo: {
    width: 60,
    height: 30,
  },
  defaultBadge: {
    backgroundColor: colors.primaryLight,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  defaultText: {
    ...typography.caption,
    color: colors.primary,
    fontWeight: "600",
  },
  cardNumber: {
    ...typography.h3,
    marginBottom: 8,
  },
  cardDetails: {
    flexDirection: "row",
    marginBottom: 16,
  },
  expiryLabel: {
    ...typography.bodySmall,
    color: colors.textSecondary,
    marginRight: 4,
  },
  expiryDate: {
    ...typography.bodySmall,
    fontWeight: "500",
  },
  cardActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: 12,
    gap: 16,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  actionText: {
    ...typography.bodySmall,
    color: colors.primary,
  },
  emptyState: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  emptyTitle: {
    ...typography.h3,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyMessage: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: 24,
  },
  addButton: {
    marginTop: 16,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
    gap: 8,
  },
  primaryButton: {
    backgroundColor: colors.primary,
  },
  buttonText: {
    ...typography.button,
    color: colors.white,
    fontWeight: "600",
  },
});