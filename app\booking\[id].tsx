import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, Alert, TouchableOpacity } from "react-native";
import { useLocalSearchParams, useRouter, Stack } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import Button from "@/components/Button";
import { useBookingStore } from "@/stores/useBookingStore";
import { Calendar, Clock, MapPin, AlertCircle, MessageCircle } from "lucide-react-native";
import { useTranslation } from "@/hooks/useTranslation";

export default function BookingDetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { t } = useTranslation();
  const { getBookingById, cancelBooking, isLoading, updateBookingStatus } = useBookingStore();
  
  const [booking, setBooking] = useState(getBookingById(id as string));
  const [acceptanceTimer, setAcceptanceTimer] = useState<number | null>(null);
  
  useEffect(() => {
    // Update booking when it changes in the store
    setBooking(getBookingById(id as string));
  }, [id, getBookingById]);
  
  useEffect(() => {
    // Simulate booking acceptance after 5 seconds if status is pending
    if (booking && booking.status === "pending") {
      const timer = setTimeout(() => {
        updateBookingStatus(id as string, "confirmed");
        setBooking(getBookingById(id as string));
        
        // Show confirmation alert
        Alert.alert(
          t("bookingAccepted"),
          t("bookingAcceptedMessage", { provider: booking.providerName }),
          [
            {
              text: t("chatWithProvider"),
              onPress: () => router.push({
                pathname: "/chat/[id]",
                params: { id: booking.providerId },
              }),
            },
            {
              text: t("ok"),
            },
          ]
        );
      }, 5000);
      
      setAcceptanceTimer(timer as unknown as number);
      
      return () => {
        if (acceptanceTimer) {
          clearTimeout(acceptanceTimer);
        }
      };
    }
  }, [booking?.status, id, t, booking?.providerName, booking?.providerId, updateBookingStatus, getBookingById, router, acceptanceTimer]);

  const handleCancelBooking = () => {
    Alert.alert(
      t("cancelBooking"),
      t("cancelBookingConfirm"),
      [
        {
          text: t("no"),
          style: "cancel",
        },
        {
          text: t("yesCancel"),
          style: "destructive",
          onPress: async () => {
            if (acceptanceTimer) {
              clearTimeout(acceptanceTimer);
            }
            await cancelBooking(id as string);
            setBooking(getBookingById(id as string));
          },
        },
      ]
    );
  };
  
  const handleChatWithProvider = () => {
    if (booking) {
      router.push({
        pathname: "/chat/[id]",
        params: { id: booking.providerId },
      });
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "";
    const options: Intl.DateTimeFormatOptions = {
      weekday: "long",
      day: "numeric",
      month: "long",
      year: "numeric",
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return "";
    const [hours, minutes] = timeString.split(":");
    const hour = parseInt(hours, 10);
    const period = hour >= 12 ? "PM" : "AM";
    const formattedHour = hour % 12 === 0 ? 12 : hour % 12;
    return `${formattedHour}:${minutes} ${period}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "confirmed":
        return colors.success;
      case "pending":
        return colors.warning;
      case "cancelled":
        return colors.error;
      case "completed":
        return colors.info;
      default:
        return colors.textSecondary;
    }
  };

  if (!booking) {
    return (
      <View style={styles.notFound}>
        <AlertCircle size={48} color={colors.error} />
        <Text style={styles.notFoundTitle}>{t("bookingNotFound")}</Text>
        <Text style={styles.notFoundMessage}>
          {t("bookingNotFoundMessage")}
        </Text>
        <Button
          title={t("goToBookings")}
          onPress={() => router.push("/bookings")}
          style={styles.notFoundButton}
        />
      </View>
    );
  }

  return (
    <>
      <Stack.Screen options={{ title: t("bookingDetails") }} />
      
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.statusContainer}>
          <View
            style={[
              styles.statusBadge,
              { backgroundColor: getStatusColor(booking.status) + "20" },
            ]}
          >
            <Text
              style={[
                styles.statusText,
                { color: getStatusColor(booking.status) },
              ]}
            >
              {t(`status.${booking.status}`)}
            </Text>
          </View>
          
          {booking.status === "pending" && (
            <Text style={styles.pendingText}>{t("waitingForConfirmation")}</Text>
          )}
        </View>
        
        <View style={styles.card}>
          <Text style={styles.cardTitle}>{t("serviceDetails")}</Text>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>{t("provider")}</Text>
            <Text style={styles.detailValue}>{booking.providerName}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>{t("service")}</Text>
            <Text style={styles.detailValue}>{booking.serviceName}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <View style={styles.iconTextContainer}>
              <Calendar size={16} color={colors.primary} />
              <Text style={styles.iconText}>{formatDate(booking.date)}</Text>
            </View>
          </View>
          
          <View style={styles.detailRow}>
            <View style={styles.iconTextContainer}>
              <Clock size={16} color={colors.primary} />
              <Text style={styles.iconText}>{formatTime(booking.time)}</Text>
            </View>
          </View>
          
          {booking.notes && (
            <View style={styles.notesContainer}>
              <Text style={styles.notesLabel}>{t("additionalNotes")}</Text>
              <Text style={styles.notesText}>{booking.notes}</Text>
            </View>
          )}
        </View>
        
        <View style={styles.card}>
          <Text style={styles.cardTitle}>{t("paymentDetails")}</Text>
          
          <View style={styles.priceRow}>
            <Text style={styles.priceLabel}>{booking.serviceName}</Text>
            <Text style={styles.priceValue}>R{booking.price}</Text>
          </View>
          
          <View style={styles.divider} />
          
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>{t("total")}</Text>
            <Text style={styles.totalValue}>R{booking.price}</Text>
          </View>
          
          <View style={styles.paymentMethod}>
            <Text style={styles.paymentMethodLabel}>{t("paymentMethod")}</Text>
            <Text style={styles.paymentMethodValue}>{t("payOnService")}</Text>
          </View>
        </View>
        
        {booking.status === "confirmed" && (
          <Button
            title={t("chatWithProvider")}
            onPress={handleChatWithProvider}
            icon={<MessageCircle size={18} color={colors.white} />}
            style={styles.chatButton}
          />
        )}
        
        {(booking.status === "pending" || booking.status === "confirmed") && (
          <Button
            title={t("cancelBooking")}
            onPress={handleCancelBooking}
            variant="outline"
            style={styles.cancelButton}
            textStyle={{ color: colors.error }}
            loading={isLoading}
            disabled={isLoading}
          />
        )}
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    padding: 16,
  },
  statusContainer: {
    alignItems: "center",
    marginBottom: 16,
  },
  statusBadge: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  statusText: {
    ...typography.body,
    fontWeight: "600",
  },
  pendingText: {
    ...typography.bodySmall,
    color: colors.textSecondary,
    marginTop: 8,
  },
  card: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  cardTitle: {
    ...typography.h3,
    marginBottom: 16,
    color: colors.primary,
  },
  detailRow: {
    marginBottom: 12,
  },
  detailLabel: {
    ...typography.bodySmall,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  detailValue: {
    ...typography.body,
    fontWeight: "500",
  },
  iconTextContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  iconText: {
    ...typography.body,
  },
  notesContainer: {
    marginTop: 8,
    padding: 12,
    backgroundColor: colors.primaryLight,
    borderRadius: 8,
  },
  notesLabel: {
    ...typography.bodySmall,
    fontWeight: "600",
    marginBottom: 4,
  },
  notesText: {
    ...typography.bodySmall,
  },
  priceRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  priceLabel: {
    ...typography.body,
  },
  priceValue: {
    ...typography.body,
  },
  divider: {
    height: 1,
    backgroundColor: colors.border,
    marginVertical: 12,
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  totalLabel: {
    ...typography.body,
    fontWeight: "600",
  },
  totalValue: {
    ...typography.h3,
    color: colors.primary,
  },
  paymentMethod: {
    padding: 12,
    backgroundColor: colors.primaryLight,
    borderRadius: 8,
  },
  paymentMethodLabel: {
    ...typography.bodySmall,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  paymentMethodValue: {
    ...typography.body,
    fontWeight: "500",
  },
  chatButton: {
    marginBottom: 16,
  },
  cancelButton: {
    marginBottom: 32,
    borderColor: colors.error,
  },
  notFound: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
  },
  notFoundTitle: {
    ...typography.h2,
    marginTop: 16,
    marginBottom: 8,
    textAlign: "center",
  },
  notFoundMessage: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: 24,
  },
  notFoundButton: {
    minWidth: 200,
  },
});