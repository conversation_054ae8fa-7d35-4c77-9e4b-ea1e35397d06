import React, { useState } from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity } from "react-native";
import { useRouter } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { useBookingStore, Booking } from "@/stores/useBookingStore";
import { Calendar, Clock, AlertCircle } from "lucide-react-native";

import { useAuthStore } from "@/stores/useAuthStore";
import { useTranslation } from "@/hooks/useTranslation";

export default function BookingsScreen() {
  const router = useRouter();
  const { bookings, cancelBooking } = useBookingStore();
  const { isAuthenticated } = useAuthStore();
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<"upcoming" | "past">("upcoming");

  // Filter bookings based on active tab
  const currentDate = new Date();
  const filteredBookings = bookings.filter((booking) => {
    const bookingDate = new Date(`${booking.date}T${booking.time}`);
    const isPast = bookingDate < currentDate || booking.status === "completed" || booking.status === "cancelled";
    return activeTab === "upcoming" ? !isPast : isPast;
  });

  const handleViewBooking = (bookingId: string) => {
    router.push({
      pathname: "/booking/[id]",
      params: { id: bookingId },
    });
  };

  const handleCancelBooking = async (bookingId: string) => {
    await cancelBooking(bookingId);
  };

  if (!isAuthenticated) {
    return (
      <View style={styles.authContainer}>
        <AlertCircle size={48} color={colors.primary} />
        <Text style={styles.authTitle}>{t("signInToViewBookings")}</Text>
        <Text style={styles.authMessage}>
          {t("signInToViewBookingsDesc")}
        </Text>
        <TouchableOpacity
          style={[styles.authButton, styles.primaryButton]}
          onPress={() => router.push("/auth/login")}
        >
          <Text style={styles.buttonText}>{t("signIn")}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === "upcoming" && styles.activeTab]}
          onPress={() => setActiveTab("upcoming")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "upcoming" && styles.activeTabText,
            ]}
          >
            {t("upcoming")}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === "past" && styles.activeTab]}
          onPress={() => setActiveTab("past")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "past" && styles.activeTabText,
            ]}
          >
            {t("past")}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {filteredBookings.length > 0 ? (
          filteredBookings.map((booking) => (
            <BookingCard
              key={booking.id}
              booking={booking}
              onView={() => handleViewBooking(booking.id)}
              onCancel={() => handleCancelBooking(booking.id)}
              isPast={activeTab === "past"}
            />
          ))
        ) : (
          <View style={styles.emptyState}>
            <Calendar size={48} color={colors.textSecondary} />
            <Text style={styles.emptyStateTitle}>{t("noBookingsFound")}</Text>
            <Text style={styles.emptyStateMessage}>
              {activeTab === "upcoming"
                ? t("noUpcomingBookings")
                : t("noPastBookings")}
            </Text>
            {activeTab === "upcoming" && (
              <TouchableOpacity
                style={[styles.browseButton, styles.outlineButton]}
                onPress={() => router.push("/browse")}
              >
                <Text style={styles.outlineButtonText}>{t("browseServices")}</Text>
              </TouchableOpacity>
            )}
          </View>
        )}
      </ScrollView>
    </View>
  );
}

interface BookingCardProps {
  booking: Booking;
  onView: () => void;
  onCancel: () => void;
  isPast: boolean;
}

function BookingCard({ booking, onView, onCancel, isPast }: BookingCardProps) {
  const { t } = useTranslation();

  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      weekday: "short",
      day: "numeric",
      month: "short",
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(":");
    const hour = parseInt(hours, 10);
    const period = hour >= 12 ? "PM" : "AM";
    const formattedHour = hour % 12 === 0 ? 12 : hour % 12;
    return `${formattedHour}:${minutes} ${period}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "confirmed":
        return colors.success;
      case "pending":
        return colors.warning;
      case "cancelled":
        return colors.error;
      case "completed":
        return colors.info;
      default:
        return colors.textSecondary;
    }
  };

  return (
    <View style={styles.bookingCard}>
      <View style={styles.bookingHeader}>
        <Text style={styles.providerName}>{booking.providerName}</Text>
        <View
          style={[
            styles.statusBadge,
            { backgroundColor: getStatusColor(booking.status) + "20" },
          ]}
        >
          <Text
            style={[
              styles.statusText,
              { color: getStatusColor(booking.status) },
            ]}
          >
            {t(`status.${booking.status}`)}
          </Text>
        </View>
      </View>

      <Text style={styles.serviceName}>{booking.serviceName}</Text>

      <View style={styles.bookingDetails}>
        <View style={styles.detailItem}>
          <Calendar size={16} color={colors.textSecondary} />
          <Text style={styles.detailText}>{formatDate(booking.date)}</Text>
        </View>
        <View style={styles.detailItem}>
          <Clock size={16} color={colors.textSecondary} />
          <Text style={styles.detailText}>{formatTime(booking.time)}</Text>
        </View>
      </View>

      <View style={styles.priceContainer}>
        <Text style={styles.priceLabel}>{t("price")}</Text>
        <Text style={styles.price}>R{booking.price}</Text>
      </View>

      <View style={styles.bookingActions}>
        <TouchableOpacity
          style={[styles.viewButton, styles.outlineButton]}
          onPress={onView}
        >
          <Text style={styles.outlineButtonText}>{t("viewDetails")}</Text>
        </TouchableOpacity>
        {!isPast && booking.status !== "cancelled" && (
          <TouchableOpacity
            style={[styles.cancelButton, styles.textButton]}
            onPress={onCancel}
          >
            <Text style={[styles.textButtonText, { color: colors.error }]}>
              {t("cancel")}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  tabsContainer: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: "center",
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  tabText: {
    ...typography.body,
    color: colors.textSecondary,
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: "600",
  },
  content: {
    flex: 1,
    padding: 16,
  },
  bookingCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  bookingHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  providerName: {
    ...typography.h4,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 16,
  },
  statusText: {
    ...typography.caption,
    fontWeight: "600",
  },
  serviceName: {
    ...typography.body,
    color: colors.textSecondary,
    marginBottom: 12,
  },
  bookingDetails: {
    flexDirection: "row",
    marginBottom: 16,
    gap: 16,
  },
  detailItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  detailText: {
    ...typography.bodySmall,
  },
  priceContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    marginBottom: 16,
  },
  priceLabel: {
    ...typography.bodySmall,
  },
  price: {
    ...typography.h4,
    color: colors.primary,
  },
  bookingActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  viewButton: {
    flex: 1,
    marginRight: 8,
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
    marginTop: 32,
  },
  emptyStateTitle: {
    ...typography.h3,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateMessage: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: 24,
  },
  browseButton: {
    minWidth: 200,
  },
  authContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
  },
  authTitle: {
    ...typography.h2,
    marginTop: 16,
    marginBottom: 8,
    textAlign: "center",
  },
  authMessage: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: 32,
  },
  authButton: {
    minWidth: 200,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  cancelButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: "center",
    justifyContent: "center",
  },
  primaryButton: {
    backgroundColor: colors.primary,
  },
  outlineButton: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: colors.primary,
  },
  textButton: {
    backgroundColor: "transparent",
  },
  buttonText: {
    ...typography.button,
    color: colors.white,
    fontWeight: "600",
  },
  outlineButtonText: {
    ...typography.button,
    color: colors.primary,
    fontWeight: "600",
  },
  textButtonText: {
    ...typography.button,
    fontWeight: "600",
  },
});