import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { Stack, useRouter } from "expo-router";
import { useTranslation } from "@/hooks/useTranslation";
import { MessageCircle } from "lucide-react-native";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import Button from "@/components/Button";
import { useAuthStore } from "@/stores/useAuthStore";

export default function ChatTabScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const { isAuthenticated } = useAuthStore();

  return (
    <>
      <Stack.Screen options={{ title: t("messages") }} />
      
      <View style={styles.container}>
        {isAuthenticated ? (
          <View style={styles.content}>
            <MessageCircle size={64} color={colors.primary} />
            <Text style={styles.title}>{t("chatTabMessage")}</Text>
            <Text style={styles.message}>{t("chatTabDescription")}</Text>
            <Button
              title={t("viewMessages")}
              onPress={() => router.push("/chat")}
              style={styles.button}
            />
          </View>
        ) : (
          <View style={styles.content}>
            <MessageCircle size={64} color={colors.primary} />
            <Text style={styles.title}>{t("signInToChat")}</Text>
            <Text style={styles.message}>{t("signInToChatDescription")}</Text>
            <Button
              title={t("signIn")}
              onPress={() => router.push("/auth/login")}
              style={styles.button}
            />
          </View>
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  content: {
    alignItems: "center",
    justifyContent: "center",
    maxWidth: 300,
  },
  title: {
    ...typography.h2,
    marginTop: 16,
    marginBottom: 8,
    textAlign: "center",
  },
  message: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: 24,
  },
  button: {
    minWidth: 200,
  },
});