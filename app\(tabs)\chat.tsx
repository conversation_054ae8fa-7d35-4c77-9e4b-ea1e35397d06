import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { Stack, useRouter } from "expo-router";
import { useTranslation } from "@/hooks/useTranslation";
import { MessageCircle } from "lucide-react-native";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { useAuthStore } from "@/stores/useAuthStore";

export default function ChatTabScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const { isAuthenticated } = useAuthStore();

  return (
    <>
      <Stack.Screen options={{ title: t("messages") }} />

      <View style={styles.container}>
        {isAuthenticated ? (
          <View style={styles.content}>
            <MessageCircle size={64} color={colors.primary} />
            <Text style={styles.title}>{t("chatTabMessage")}</Text>
            <Text style={styles.message}>{t("chatTabDescription")}</Text>
            <TouchableOpacity
              style={[styles.button, styles.primaryButton]}
              onPress={() => router.push("/chat")}
            >
              <Text style={styles.buttonText}>{t("viewMessages")}</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.content}>
            <MessageCircle size={64} color={colors.primary} />
            <Text style={styles.title}>{t("signInToChat")}</Text>
            <Text style={styles.message}>{t("signInToChatDescription")}</Text>
            <TouchableOpacity
              style={[styles.button, styles.primaryButton]}
              onPress={() => router.push("/auth/login")}
            >
              <Text style={styles.buttonText}>{t("signIn")}</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  content: {
    alignItems: "center",
    justifyContent: "center",
    maxWidth: 300,
  },
  title: {
    ...typography.h2,
    marginTop: 16,
    marginBottom: 8,
    textAlign: "center",
  },
  message: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: 24,
  },
  button: {
    minWidth: 200,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  primaryButton: {
    backgroundColor: colors.primary,
  },
  buttonText: {
    ...typography.button,
    color: colors.white,
  },
});