import React, { useState } from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity } from "react-native";
import { useRouter } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { serviceCategories, ServiceCategory } from "@/mocks/categories";
import { serviceProviders } from "@/mocks/providers";
import SearchBar from "@/components/SearchBar";
import CategoryDropdown from "@/components/CategoryDropdown";
import ProviderCard from "@/components/ProviderCard";
import { Filter, SlidersHorizontal } from "lucide-react-native";
import { useTranslation } from "@/hooks/useTranslation";

export default function BrowseScreen() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<ServiceCategory | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const { t } = useTranslation();

  // Filter providers based on search and category
  const filteredProviders = serviceProviders.filter((provider) => {
    const matchesSearch = searchQuery
      ? t(provider.nameKey).toLowerCase().includes(searchQuery.toLowerCase()) ||
        provider.services.some((service) =>
          t(service.nameKey).toLowerCase().includes(searchQuery.toLowerCase())
        )
      : true;

    const matchesCategory = selectedCategory
      ? provider.categoryId === selectedCategory.id
      : true;

    return matchesSearch && matchesCategory;
  });

  const handleProviderPress = (provider: any) => {
    router.push({
      pathname: "/provider/[id]",
      params: { id: provider.id },
    });
  };

  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <SearchBar
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder={t("searchServicesOrProviders")}
        />
        <TouchableOpacity style={styles.filterButton} onPress={toggleFilters}>
          <SlidersHorizontal size={20} color={colors.text} />
        </TouchableOpacity>
      </View>

      {showFilters && (
        <View style={styles.filtersContainer}>
          <Text style={styles.filtersTitle}>{t("filters")}</Text>
          <View style={styles.filterRow}>
            <Text style={styles.filterLabel}>{t("category")}</Text>
            <CategoryDropdown
              categories={serviceCategories}
              selectedCategory={selectedCategory}
              onSelect={setSelectedCategory}
            />
          </View>
          
          <View style={styles.filterActions}>
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => {
                setSelectedCategory(null);
                setSearchQuery("");
              }}
            >
              <Text style={styles.clearButtonText}>{t("clearAll")}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.applyButton}
              onPress={() => setShowFilters(false)}
            >
              <Text style={styles.applyButtonText}>{t("apply")}</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.resultsHeader}>
          <Text style={styles.resultsTitle}>
            {selectedCategory
              ? t("categoryProviders", { category: t(selectedCategory.nameKey) })
              : t("allServiceProviders")}
          </Text>
          <Text style={styles.resultsCount}>
            {filteredProviders.length} {filteredProviders.length === 1 ? t("result") : t("results")}
          </Text>
        </View>

        {filteredProviders.length > 0 ? (
          <View style={styles.providersList}>
            {filteredProviders.map((provider) => (
              <ProviderCard
                key={provider.id}
                provider={provider}
                onPress={handleProviderPress}
              />
            ))}
          </View>
        ) : (
          <View style={styles.emptyState}>
            <Filter size={48} color={colors.textSecondary} />
            <Text style={styles.emptyStateTitle}>{t("noProvidersFound")}</Text>
            <Text style={styles.emptyStateMessage}>
              {t("tryAdjustingSearch")}
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    gap: 12,
  },
  filterButton: {
    padding: 12,
    backgroundColor: colors.card,
    borderRadius: 8,
  },
  filtersContainer: {
    backgroundColor: colors.white,
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  filtersTitle: {
    ...typography.h3,
    marginBottom: 16,
  },
  filterRow: {
    marginBottom: 16,
    gap: 8,
  },
  filterLabel: {
    ...typography.body,
    fontWeight: "500",
    marginBottom: 4,
  },
  filterActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: 12,
    marginTop: 8,
  },
  clearButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  clearButtonText: {
    ...typography.body,
    color: colors.textSecondary,
  },
  applyButton: {
    backgroundColor: colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  applyButtonText: {
    ...typography.body,
    color: colors.white,
    fontWeight: "600",
  },
  content: {
    flex: 1,
  },
  resultsHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  resultsTitle: {
    ...typography.h3,
  },
  resultsCount: {
    ...typography.bodySmall,
    color: colors.textSecondary,
  },
  providersList: {
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
    marginTop: 32,
  },
  emptyStateTitle: {
    ...typography.h3,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateMessage: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: "center",
  },
});