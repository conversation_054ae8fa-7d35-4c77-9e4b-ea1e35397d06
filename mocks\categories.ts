export interface ServiceCategory {
  id: string;
  nameKey: string; // Translation key for name
  icon: string;
  descriptionKey: string; // Translation key for description
  popularServicesKeys: string[]; // Translation keys for popular services
}

export const serviceCategories: ServiceCategory[] = [
  {
    id: "home-services",
    nameKey: "category.homeServices",
    icon: "home",
    descriptionKey: "category.homeServicesDesc",
    popularServicesKeys: [
      "service.cleaning", 
      "service.plumbing", 
      "service.electrical", 
      "service.gardening", 
      "service.painting"
    ]
  },
  {
    id: "personal-care",
    nameKey: "category.personalCare",
    icon: "scissors",
    descriptionKey: "category.personalCareDesc",
    popularServicesKeys: [
      "service.haircut", 
      "service.massage", 
      "service.manicure", 
      "service.makeup", 
      "service.personalTraining"
    ]
  },
  {
    id: "professional",
    nameKey: "category.professional",
    icon: "briefcase",
    descriptionKey: "category.professionalDesc",
    popularServicesKeys: [
      "service.accounting", 
      "service.legalAdvice", 
      "service.marketing", 
      "service.itSupport", 
      "service.design"
    ]
  },
  {
    id: "education",
    nameKey: "category.education",
    icon: "book-open",
    descriptionKey: "category.educationDesc",
    popularServicesKeys: [
      "service.mathTutoring", 
      "service.languageLessons", 
      "service.musicLessons", 
      "service.testPrep", 
      "service.coding"
    ]
  },
  {
    id: "events",
    nameKey: "category.events",
    icon: "calendar",
    descriptionKey: "category.eventsDesc",
    popularServicesKeys: [
      "service.photography", 
      "service.catering", 
      "service.djServices", 
      "service.eventPlanning", 
      "service.decoration"
    ]
  },
  {
    id: "automotive",
    nameKey: "category.automotive",
    icon: "car",
    descriptionKey: "category.automotiveDesc",
    popularServicesKeys: [
      "service.carWash", 
      "service.mechanic", 
      "service.towing", 
      "service.tireChange", 
      "service.detailing"
    ]
  },
  {
    id: "delivery",
    nameKey: "category.delivery",
    icon: "package",
    descriptionKey: "category.deliveryDesc",
    popularServicesKeys: [
      "service.foodDelivery", 
      "service.groceryDelivery", 
      "service.courier", 
      "service.moving", 
      "service.furnitureAssembly"
    ]
  },
  {
    id: "tech-support",
    nameKey: "category.techSupport",
    icon: "cpu",
    descriptionKey: "category.techSupportDesc",
    popularServicesKeys: [
      "service.computerRepair", 
      "service.phoneRepair", 
      "service.networkSetup", 
      "service.smartHome", 
      "service.dataRecovery"
    ]
  }
];