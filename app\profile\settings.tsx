import React, { useState } from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Switch } from "react-native";
import { Stack, useRouter } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { ChevronRight, Moon, Globe, Volume2, Smartphone, Database } from "lucide-react-native";
import { useThemeStore } from "@/stores/useThemeStore";
import { useTranslation } from "@/hooks/useTranslation";

export default function SettingsScreen() {
  const router = useRouter();
  const { isDarkMode, toggleDarkMode } = useThemeStore();
  const { t } = useTranslation();
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [vibrationEnabled, setVibrationEnabled] = useState(true);

  return (
    <>
      <Stack.Screen options={{ title: t("settings") }} />
      
      <ScrollView style={styles.container}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t("appearance")}</Text>
          
          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => router.push("/profile/language")}
          >
            <View style={styles.settingLeft}>
              <Globe size={20} color={colors.text} />
              <Text style={styles.settingText}>{t("language")}</Text>
            </View>
            <View style={styles.settingRight}>
              <ChevronRight size={20} color={colors.textSecondary} />
            </View>
          </TouchableOpacity>
          
          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <Moon size={20} color={colors.text} />
              <Text style={styles.settingText}>{t("darkMode")}</Text>
            </View>
            <Switch
              value={isDarkMode}
              onValueChange={toggleDarkMode}
              trackColor={{ false: colors.border, true: colors.primaryLight }}
              thumbColor={isDarkMode ? colors.primary : colors.card}
            />
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t("sound")}</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <Volume2 size={20} color={colors.text} />
              <Text style={styles.settingText}>{t("inAppSounds")}</Text>
            </View>
            <Switch
              value={soundEnabled}
              onValueChange={setSoundEnabled}
              trackColor={{ false: colors.border, true: colors.primaryLight }}
              thumbColor={soundEnabled ? colors.primary : colors.card}
            />
          </View>
          
          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <Smartphone size={20} color={colors.text} />
              <Text style={styles.settingText}>{t("vibration")}</Text>
            </View>
            <Switch
              value={vibrationEnabled}
              onValueChange={setVibrationEnabled}
              trackColor={{ false: colors.border, true: colors.primaryLight }}
              thumbColor={vibrationEnabled ? colors.primary : colors.card}
            />
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t("storage")}</Text>
          
          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => {
              // Show confirmation dialog
              alert(t("cacheCleared"));
            }}
          >
            <View style={styles.settingLeft}>
              <Database size={20} color={colors.text} />
              <View>
                <Text style={styles.settingText}>{t("clearCache")}</Text>
                <Text style={styles.settingDescription}>
                  {t("clearCacheDescription")}
                </Text>
              </View>
            </View>
            <Text style={styles.cacheSize}>24 MB</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    ...typography.h4,
    paddingHorizontal: 16,
    paddingVertical: 12,
    color: colors.textSecondary,
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  settingLeft: {
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
    flex: 1,
  },
  settingText: {
    ...typography.body,
  },
  settingDescription: {
    ...typography.caption,
    color: colors.textSecondary,
  },
  settingRight: {
    flexDirection: "row",
    alignItems: "center",
  },
  cacheSize: {
    ...typography.bodySmall,
    color: colors.textSecondary,
  },
});