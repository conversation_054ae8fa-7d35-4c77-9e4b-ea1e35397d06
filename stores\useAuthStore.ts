import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";

export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar: string | null;
  location: string;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string) => Promise<void>;
  logout: () => void;
  updateProfile: (userData: Partial<User>) => void;
}

// Mock user for demo purposes
const mockUser: User = {
  id: "user123",
  name: "<PERSON>",
  email: "<EMAIL>",
  phone: "+27 71 234 5678",
  avatar: "https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80",
  location: "location.capeTown",
};

// Create a separate store for non-persisted state
const useAuthStoreBase = create<AuthState>((set) => ({
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,

  login: async (email, password) => {
    set({ isLoading: true, error: null });
    
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      
      // For demo, we'll just check if email contains "error" to simulate an error
      if (email.includes("error")) {
        throw new Error("Invalid credentials");
      }
      
      set({ 
        user: mockUser,
        isAuthenticated: true,
        isLoading: false 
      });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : "An error occurred",
        isLoading: false 
      });
    }
  },

  register: async (name, email, password) => {
    set({ isLoading: true, error: null });
    
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      
      // For demo, we'll just check if email contains "error" to simulate an error
      if (email.includes("error")) {
        throw new Error("Email already in use");
      }
      
      const newUser = {
        ...mockUser,
        name,
        email,
      };
      
      set({ 
        user: newUser,
        isAuthenticated: true,
        isLoading: false 
      });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : "An error occurred",
        isLoading: false 
      });
    }
  },

  logout: () => {
    set({ 
      user: null,
      isAuthenticated: false 
    });
  },

  updateProfile: (userData) => {
    set((state) => ({
      user: state.user ? { ...state.user, ...userData } : null,
    }));
  },
}));

// Create a persisted version of the store with optimized storage
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      ...useAuthStoreBase.getState(),
      
      // Override methods that need special handling for persistence
      updateProfile: (userData) => {
        // Get current state
        const currentState = get();
        const currentUser = currentState.user;
        
        // If we're updating with a new avatar that's a data URI (from camera/image picker)
        if (userData.avatar && userData.avatar.startsWith('data:image')) {
          // For data URIs, store a reference instead of the full data
          // In a real app, you would upload this to a server and store the URL
          console.log('Storing image reference instead of full data URI');
          
          // For demo purposes, we'll just truncate the data URI to avoid storage issues
          // In a real app, you would upload the image and store the URL
          userData = {
            ...userData,
            avatar: 'custom_image_' + Date.now()
          };
        }
        
        // Update the state with the modified data
        set({
          user: currentUser ? { ...currentUser, ...userData } : null
        });
      },
      
      login: async (email, password) => {
        set({ isLoading: true, error: null });
        
        try {
          // Simulate API call
          await new Promise((resolve) => setTimeout(resolve, 1000));
          
          // For demo, we'll just check if email contains "error" to simulate an error
          if (email.includes("error")) {
            throw new Error("Invalid credentials");
          }
          
          set({ 
            user: mockUser,
            isAuthenticated: true,
            isLoading: false 
          });
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : "An error occurred",
            isLoading: false 
          });
        }
      },

      register: async (name, email, password) => {
        set({ isLoading: true, error: null });
        
        try {
          // Simulate API call
          await new Promise((resolve) => setTimeout(resolve, 1000));
          
          // For demo, we'll just check if email contains "error" to simulate an error
          if (email.includes("error")) {
            throw new Error("Email already in use");
          }
          
          const newUser = {
            ...mockUser,
            name,
            email,
          };
          
          set({ 
            user: newUser,
            isAuthenticated: true,
            isLoading: false 
          });
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : "An error occurred",
            isLoading: false 
          });
        }
      },

      logout: () => {
        set({ 
          user: null,
          isAuthenticated: false 
        });
      },
    }),
    {
      name: "auth-storage",
      storage: createJSONStorage(() => AsyncStorage),
      // Only persist essential data
      partialize: (state) => ({
        user: state.user ? {
          ...state.user,
          // Don't persist large data URIs
          avatar: state.user.avatar && state.user.avatar.startsWith('data:image') 
            ? 'custom_image_reference' 
            : state.user.avatar
        } : null,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);