import React from "react";
import { StyleSheet, View, TextInput, TouchableOpacity } from "react-native";
import { Search, X } from "lucide-react-native";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { useTranslation } from "@/hooks/useTranslation";

interface SearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  onClear?: () => void;
  onSubmit?: () => void;
}

export default function SearchBar({
  value,
  onChangeText,
  placeholder,
  onClear,
  onSubmit,
}: SearchBarProps) {
  const { t } = useTranslation();
  
  return (
    <View style={styles.container}>
      <Search size={20} color={colors.textSecondary} style={styles.searchIcon} />
      <TextInput
        style={styles.input}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder || t("search")}
        placeholderTextColor={colors.textSecondary}
        returnKeyType="search"
        onSubmitEditing={onSubmit}
      />
      {value.length > 0 && onClear && (
        <TouchableOpacity style={styles.clearButton} onPress={onClear}>
          <X size={18} color={colors.textSecondary} />
        </TouchableOpacity>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.card,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    flex: 1,
  },
  searchIcon: {
    marginRight: 8,
  },
  input: {
    ...typography.body,
    flex: 1,
    padding: 4,
    color: colors.text,
  },
  clearButton: {
    padding: 4,
  },
});