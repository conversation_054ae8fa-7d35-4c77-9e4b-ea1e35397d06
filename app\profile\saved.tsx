import React, { useState } from "react";
import { StyleSheet, Text, View, FlatList, TouchableOpacity, Image } from "react-native";
import { Stack, useRouter } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { serviceProviders } from "@/mocks/providers";
import { Star, MapPin, Search, Heart } from "lucide-react-native";
import SearchBar from "@/components/SearchBar";

import { useTranslation } from "@/hooks/useTranslation";

// Mock saved providers (first 3 providers)
const savedProviders = serviceProviders.slice(0, 3);

export default function SavedProvidersScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState("");

  // Filter providers based on search
  const filteredProviders = savedProviders.filter((provider) =>
    t(provider.nameKey).toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleProviderPress = (provider: any) => {
    router.push({
      pathname: "/provider/[id]",
      params: { id: provider.id },
    });
  };

  const handleRemove = (providerId: string) => {
    // In a real app, this would remove the provider from saved list
    alert(t("providerRemoved"));
  };

  return (
    <>
      <Stack.Screen options={{ title: t("savedProviders") }} />

      <View style={styles.container}>
        <View style={styles.searchContainer}>
          <SearchBar
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder={t("searchSavedProviders")}
          />
        </View>

        {savedProviders.length > 0 ? (
          filteredProviders.length > 0 ? (
            <FlatList
              data={filteredProviders}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.providerCard}
                  onPress={() => handleProviderPress(item)}
                >
                  <Image source={{ uri: item.avatar }} style={styles.avatar} />

                  <View style={styles.providerInfo}>
                    <Text style={styles.providerName}>{t(item.nameKey)}</Text>

                    <View style={styles.ratingContainer}>
                      <Star
                        size={14}
                        color={colors.secondary}
                        fill={colors.secondary}
                      />
                      <Text style={styles.rating}>
                        {item.rating} ({item.reviewCount})
                      </Text>
                    </View>

                    <View style={styles.locationContainer}>
                      <MapPin size={14} color={colors.textSecondary} />
                      <Text style={styles.location}>{t(item.locationKey)}</Text>
                    </View>
                  </View>

                  <TouchableOpacity
                    style={styles.removeButton}
                    onPress={() => handleRemove(item.id)}
                  >
                    <Heart size={20} color={colors.error} fill={colors.error} />
                  </TouchableOpacity>
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.providersList}
            />
          ) : (
            <View style={styles.emptySearch}>
              <Search size={48} color={colors.textSecondary} />
              <Text style={styles.emptyTitle}>{t("noSearchResults")}</Text>
              <Text style={styles.emptyMessage}>{t("tryDifferentSearch")}</Text>
            </View>
          )
        ) : (
          <View style={styles.emptyState}>
            <Heart size={48} color={colors.textSecondary} />
            <Text style={styles.emptyTitle}>{t("noSavedProviders")}</Text>
            <Text style={styles.emptyMessage}>
              {t("saveProvidersDescription")}
            </Text>
            <TouchableOpacity
              style={[styles.browseButton, styles.primaryButton]}
              onPress={() => router.push("/browse")}
            >
              <Text style={styles.buttonText}>{t("browseProviders")}</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  searchContainer: {
    padding: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  providersList: {
    padding: 16,
  },
  providerCard: {
    flexDirection: "row",
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  avatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    marginRight: 16,
  },
  providerInfo: {
    flex: 1,
    justifyContent: "center",
  },
  providerName: {
    ...typography.body,
    fontWeight: "600",
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
    gap: 4,
  },
  rating: {
    ...typography.caption,
  },
  locationContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  location: {
    ...typography.caption,
    color: colors.textSecondary,
  },
  removeButton: {
    padding: 8,
    alignSelf: "center",
  },
  emptyState: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
  },
  emptySearch: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
  },
  emptyTitle: {
    ...typography.h3,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyMessage: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: 24,
  },
  browseButton: {
    minWidth: 200,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  primaryButton: {
    backgroundColor: colors.primary,
  },
  buttonText: {
    ...typography.button,
    color: colors.white,
    fontWeight: "600",
  },
});