import React, { useState } from "react";
import { StyleSheet, Text, View, TouchableOpacity, Modal, FlatList } from "react-native";
import { ServiceCategory } from "@/mocks/categories";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { ChevronDown, Check } from "lucide-react-native";
import { useTranslation } from "@/hooks/useTranslation";

interface CategoryDropdownProps {
  categories: ServiceCategory[];
  selectedCategory: ServiceCategory | null;
  onSelect: (category: ServiceCategory | null) => void;
}

// Create a type for the "All Categories" option
interface AllCategoriesOption {
  id: string;
  nameKey: string;
  icon: string;
  descriptionKey: string;
  popularServicesKeys: string[];
}

export default function CategoryDropdown({
  categories,
  selectedCategory,
  onSelect,
}: CategoryDropdownProps) {
  const [modalVisible, setModalVisible] = useState(false);
  const { t } = useTranslation();

  // Create the "All Categories" option with the same shape as ServiceCategory
  const allCategoriesOption: AllCategoriesOption = {
    id: "all",
    nameKey: "allCategories",
    icon: "grid",
    descriptionKey: "allCategoriesDesc",
    popularServicesKeys: [],
  };

  const handleSelect = (category: ServiceCategory | null) => {
    onSelect(category);
    setModalVisible(false);
  };

  return (
    <View>
      <TouchableOpacity
        style={styles.dropdown}
        onPress={() => setModalVisible(true)}
      >
        <Text style={styles.dropdownText}>
          {selectedCategory ? t(selectedCategory.nameKey) : t("allCategories")}
        </Text>
        <ChevronDown size={20} color={colors.text} />
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t("selectCategory")}</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.closeButtonText}>{t("cancel")}</Text>
              </TouchableOpacity>
            </View>

            <FlatList
              data={[allCategoriesOption, ...categories]}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.categoryItem}
                  onPress={() =>
                    handleSelect(item.id === "all" ? null : item)
                  }
                >
                  <Text style={styles.categoryText}>{t(item.nameKey)}</Text>
                  {(selectedCategory?.id === item.id ||
                    (!selectedCategory && item.id === "all")) && (
                    <Check size={20} color={colors.primary} />
                  )}
                </TouchableOpacity>
              )}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  dropdown: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: colors.white,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  dropdownText: {
    ...typography.body,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: "80%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    ...typography.h3,
  },
  closeButton: {
    padding: 4,
  },
  closeButtonText: {
    ...typography.body,
    color: colors.primary,
  },
  categoryItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  categoryText: {
    ...typography.body,
  },
});