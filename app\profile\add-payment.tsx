import React, { useState } from "react";
import { StyleSheet, Text, View, ScrollView, Alert, TouchableOpacity } from "react-native";
import { Stack, useRouter } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { useTranslation } from "@/hooks/useTranslation";

import CardDetailsForm from "@/components/CardDetailsForm";

export default function AddPaymentMethodScreen() {
  const router = useRouter();
  const { t } = useTranslation();

  const [isCardValid, setIsCardValid] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isDefault, setIsDefault] = useState(true);

  const handleSave = async () => {
    if (!isCardValid) {
      Alert.alert(t("error"), t("invalidCardDetails"));
      return;
    }

    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      Alert.alert(t("success"), t("paymentMethodAdded"));
      router.back();
    }, 1500);
  };

  return (
    <>
      <Stack.Screen options={{ title: t("addPaymentMethod") }} />

      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.headerText}>{t("addNewCard")}</Text>
          <Text style={styles.headerDescription}>{t("addCardDescription")}</Text>
        </View>

        <CardDetailsForm
          onComplete={setIsCardValid}
          setAsDefault={isDefault}
          onSetDefaultChange={setIsDefault}
        />

        <View style={styles.securityNote}>
          <Text style={styles.securityText}>{t("securePaymentNote")}</Text>
        </View>

        <TouchableOpacity
          style={[styles.saveButton, styles.primaryButton]}
          onPress={handleSave}
          disabled={!isCardValid || isLoading}
        >
          <Text style={styles.buttonText}>
            {isLoading ? t("saving") : t("saveCard")}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  headerText: {
    ...typography.h2,
    marginBottom: 8,
  },
  headerDescription: {
    ...typography.body,
    color: colors.textSecondary,
  },
  securityNote: {
    marginTop: 24,
    marginBottom: 32,
    padding: 16,
    backgroundColor: colors.primaryLight,
    borderRadius: 8,
  },
  securityText: {
    ...typography.bodySmall,
    color: colors.primary,
    textAlign: "center",
  },
  saveButton: {
    marginBottom: 32,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  primaryButton: {
    backgroundColor: colors.primary,
  },
  buttonText: {
    ...typography.button,
    color: colors.white,
    fontWeight: "600",
  },
});