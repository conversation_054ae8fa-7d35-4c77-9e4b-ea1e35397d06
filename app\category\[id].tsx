import React from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { serviceCategories } from "@/mocks/categories";
import { serviceProviders } from "@/mocks/providers";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import ProviderCard from "@/components/ProviderCard";
import { ChevronLeft } from "lucide-react-native";
import { useTranslation } from "@/hooks/useTranslation";

export default function CategoryScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const { t } = useTranslation();

  // Find the category by ID
  const category = serviceCategories.find((c) => c.id === id);

  // Get providers for this category
  const categoryProviders = serviceProviders.filter(
    (p) => p.categoryId === id
  );

  if (!category) {
    return (
      <View style={styles.notFoundContainer}>
        <Text style={styles.notFoundText}>{t("categoryNotFound")}</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>{t("goBack")}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const handleProviderPress = (provider: any) => {
    router.push({
      pathname: "/provider/[id]",
      params: { id: provider.id },
    });
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ChevronLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.title}>{t(category.nameKey)}</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Text style={styles.description}>{t(category.descriptionKey)}</Text>

        <View style={styles.popularServicesSection}>
          <Text style={styles.sectionTitle}>{t("popularServices")}</Text>
          <View style={styles.popularServicesList}>
            {category.popularServicesKeys.map((serviceKey, index) => (
              <View key={index} style={styles.popularServiceItem}>
                <Text style={styles.popularServiceText}>{t(serviceKey)}</Text>
              </View>
            ))}
          </View>
        </View>

        <View style={styles.providersSection}>
          <Text style={styles.sectionTitle}>
            {t("providers")} ({categoryProviders.length})
          </Text>
          {categoryProviders.length > 0 ? (
            categoryProviders.map((provider) => (
              <ProviderCard
                key={provider.id}
                provider={provider}
                onPress={handleProviderPress}
              />
            ))
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>
                {t("noProvidersInCategory")}
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: 4,
  },
  title: {
    ...typography.h2,
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  description: {
    ...typography.body,
    marginBottom: 24,
  },
  popularServicesSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    ...typography.h3,
    marginBottom: 16,
  },
  popularServicesList: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  popularServiceItem: {
    backgroundColor: colors.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
  },
  popularServiceText: {
    ...typography.bodySmall,
    color: colors.primary,
  },
  providersSection: {
    marginBottom: 24,
  },
  emptyState: {
    padding: 24,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors.card,
    borderRadius: 12,
  },
  emptyStateText: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: "center",
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  notFoundText: {
    ...typography.h2,
    marginBottom: 16,
    textAlign: "center",
  },
  backButtonText: {
    ...typography.button,
    color: colors.primary,
  },
});