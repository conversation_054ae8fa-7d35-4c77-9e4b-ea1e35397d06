import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, FlatList, TouchableOpacity, Image } from "react-native";
import { Stack, useRouter } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { useTranslation } from "@/hooks/useTranslation";
import { Search, MessageCircle } from "lucide-react-native";
import SearchBar from "@/components/SearchBar";
import { serviceProviders } from "@/mocks/providers";
import { useAuthStore } from "@/stores/useAuthStore";
import { useBookingStore } from "@/stores/useBookingStore";

interface ChatPreview {
  id: string;
  providerId: string;
  providerName: string;
  providerAvatar: string;
  lastMessage: string;
  timestamp: string;
  unread: number;
}

export default function ChatListScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const { isAuthenticated } = useAuthStore();
  const { bookings } = useBookingStore();
  
  const [searchQuery, setSearchQuery] = useState("");
  const [chats, setChats] = useState<ChatPreview[]>([]);
  
  // Generate chat previews from bookings and static data
  useEffect(() => {
    if (!isAuthenticated) return;
    
    const chatPreviews: ChatPreview[] = [];
    
    // Add chats from confirmed bookings
    const confirmedBookings = bookings.filter(b => b.status === "confirmed");
    confirmedBookings.forEach(booking => {
      const provider = serviceProviders.find(p => p.id === booking.providerId);
      if (provider) {
        chatPreviews.push({
          id: `chat-${booking.id}`,
          providerId: booking.providerId,
          providerName: t(provider.nameKey),
          providerAvatar: provider.avatar,
          lastMessage: t("bookingConfirmedChat"),
          timestamp: "Today",
          unread: Math.random() > 0.7 ? 1 : 0, // Random unread for demo
        });
      }
    });
    
    // Add support chat
    chatPreviews.push({
      id: "support",
      providerId: "support",
      providerName: t("supportTeam"),
      providerAvatar: "https://images.unsplash.com/photo-**********-0b93528c311a?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80",
      lastMessage: t("chatPreview3"),
      timestamp: "2 days ago",
      unread: 0,
    });
    
    // Add static chats if no bookings
    if (chatPreviews.length === 1) { // Only support chat
      const provider1 = serviceProviders[0];
      const provider2 = serviceProviders[2];
      
      chatPreviews.unshift({
        id: "chat1",
        providerId: provider1.id,
        providerName: t(provider1.nameKey),
        providerAvatar: provider1.avatar,
        lastMessage: t("chatPreview1"),
        timestamp: "10:30 AM",
        unread: 2,
      });
      
      chatPreviews.unshift({
        id: "chat2",
        providerId: provider2.id,
        providerName: t(provider2.nameKey),
        providerAvatar: provider2.avatar,
        lastMessage: t("chatPreview2"),
        timestamp: "Yesterday",
        unread: 0,
      });
    }
    
    setChats(chatPreviews);
  }, [isAuthenticated, bookings, t]);
  
  // Filter chats based on search
  const filteredChats = chats.filter(chat =>
    chat.providerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    chat.lastMessage.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  const handleChatPress = (chat: ChatPreview) => {
    if (chat.providerId === "support") {
      router.push("/chat/support");
    } else {
      router.push({
        pathname: "/chat/[id]",
        params: { id: chat.providerId },
      });
    }
  };
  
  if (!isAuthenticated) {
    return (
      <View style={styles.authContainer}>
        <MessageCircle size={64} color={colors.primary} />
        <Text style={styles.authTitle}>{t("signInToChat")}</Text>
        <Text style={styles.authMessage}>{t("signInToChatDescription")}</Text>
        <TouchableOpacity
          style={styles.authButton}
          onPress={() => router.push("/auth/login")}
        >
          <Text style={styles.authButtonText}>{t("signIn")}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen options={{ title: t("messages") }} />
      
      <View style={styles.container}>
        <View style={styles.searchContainer}>
          <SearchBar
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder={t("searchConversations")}
          />
        </View>
        
        {filteredChats.length > 0 ? (
          <FlatList
            data={filteredChats}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.chatItem}
                onPress={() => handleChatPress(item)}
              >
                <Image source={{ uri: item.providerAvatar }} style={styles.avatar} />
                
                {item.unread > 0 && (
                  <View style={styles.unreadBadge}>
                    <Text style={styles.unreadText}>{item.unread}</Text>
                  </View>
                )}
                
                <View style={styles.chatInfo}>
                  <View style={styles.chatHeader}>
                    <Text style={styles.providerName} numberOfLines={1}>
                      {item.providerName}
                    </Text>
                    <Text style={styles.timestamp}>{item.timestamp}</Text>
                  </View>
                  
                  <Text
                    style={[
                      styles.lastMessage,
                      item.unread > 0 && styles.unreadMessage,
                    ]}
                    numberOfLines={1}
                  >
                    {item.lastMessage}
                  </Text>
                </View>
              </TouchableOpacity>
            )}
            contentContainerStyle={styles.chatList}
          />
        ) : (
          <View style={styles.emptyState}>
            <Search size={48} color={colors.textSecondary} />
            <Text style={styles.emptyTitle}>
              {searchQuery ? t("noChatsFound") : t("noChatsYet")}
            </Text>
            <Text style={styles.emptyMessage}>
              {searchQuery ? t("tryDifferentSearch") : t("startChatDescription")}
            </Text>
          </View>
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  searchContainer: {
    padding: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  chatList: {
    paddingVertical: 8,
  },
  chatItem: {
    flexDirection: "row",
    padding: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    position: "relative",
  },
  avatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    marginRight: 16,
  },
  unreadBadge: {
    position: "absolute",
    top: 16,
    left: 56,
    backgroundColor: colors.primary,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: colors.white,
  },
  unreadText: {
    ...typography.caption,
    color: colors.white,
    fontWeight: "bold",
  },
  chatInfo: {
    flex: 1,
    justifyContent: "center",
  },
  chatHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 4,
  },
  providerName: {
    ...typography.body,
    fontWeight: "600",
    flex: 1,
    marginRight: 8,
  },
  timestamp: {
    ...typography.caption,
    color: colors.textSecondary,
  },
  lastMessage: {
    ...typography.bodySmall,
    color: colors.textSecondary,
  },
  unreadMessage: {
    fontWeight: "600",
    color: colors.text,
  },
  emptyState: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
  },
  emptyTitle: {
    ...typography.h3,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyMessage: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: "center",
  },
  authContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
  },
  authTitle: {
    ...typography.h2,
    marginTop: 16,
    marginBottom: 8,
    textAlign: "center",
  },
  authMessage: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: 32,
  },
  authButton: {
    backgroundColor: colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  authButtonText: {
    ...typography.button,
    color: colors.white,
  },
});