import React from "react";
import { StyleSheet, Text, View, ScrollView, Image, TouchableOpacity } from "react-native";
import { useRouter } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { useAuthStore } from "@/stores/useAuthStore";

import {
  User,
  Settings,
  CreditCard,
  HelpCircle,
  LogOut,
  ChevronRight,
  Bell,
  Shield,
  Heart,
} from "lucide-react-native";
import { useTranslation } from "@/hooks/useTranslation";

export default function ProfileScreen() {
  const router = useRouter();
  const { user, isAuthenticated, logout } = useAuthStore();
  const { t } = useTranslation();

  const handleLogin = () => {
    router.push("/auth/login");
  };

  const handleLogout = () => {
    logout();
  };

  const handleEditProfile = () => {
    router.push("/profile/edit");
  };

  const menuItems = [
    {
      icon: <Settings size={20} color={colors.text} />,
      title: t("settings"),
      onPress: () => router.push("/profile/settings"),
    },
    {
      icon: <CreditCard size={20} color={colors.text} />,
      title: t("paymentMethods"),
      onPress: () => router.push("/profile/payment"),
    },
    {
      icon: <Bell size={20} color={colors.text} />,
      title: t("notifications"),
      onPress: () => router.push("/profile/notifications"),
    },
    {
      icon: <Shield size={20} color={colors.text} />,
      title: t("privacySecurity"),
      onPress: () => router.push("/profile/privacy"),
    },
    {
      icon: <Heart size={20} color={colors.text} />,
      title: t("savedProviders"),
      onPress: () => router.push("/profile/saved"),
    },
    {
      icon: <HelpCircle size={20} color={colors.text} />,
      title: t("helpSupport"),
      onPress: () => router.push("/profile/help"),
    },
  ];

  if (!isAuthenticated) {
    return (
      <View style={styles.authContainer}>
        <User size={64} color={colors.primary} />
        <Text style={styles.authTitle}>{t("signInToAccount")}</Text>
        <Text style={styles.authMessage}>
          {t("signInToAccessProfile")}
        </Text>
        <TouchableOpacity
          style={[styles.authButton, styles.primaryButton]}
          onPress={handleLogin}
        >
          <Text style={styles.buttonText}>{t("signIn")}</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => router.push("/auth/register")}>
          <Text style={styles.registerText}>
            {t("dontHaveAccount")} <Text style={styles.registerLink}>{t("register")}</Text>
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <View style={styles.profileInfo}>
          <TouchableOpacity onPress={handleEditProfile}>
            {user?.avatar ? (
              <Image
                source={{
                  uri: user.avatar,
                }}
                style={styles.avatar}
              />
            ) : (
              <View style={styles.placeholderAvatar}>
                <User size={32} color={colors.white} />
              </View>
            )}
          </TouchableOpacity>
          <View style={styles.userInfo}>
            <Text style={styles.userName}>{user?.name}</Text>
            <Text style={styles.userEmail}>{user?.email}</Text>
            <Text style={styles.userLocation}>{user?.location ? t(user.location) : t("location.currentLocation")}</Text>
          </View>
        </View>
        <TouchableOpacity
          style={[styles.editButton, styles.outlineButton]}
          onPress={handleEditProfile}
        >
          <Text style={styles.outlineButtonText}>{t("editProfile")}</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.menuSection}>
        {menuItems.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={styles.menuItem}
            onPress={item.onPress}
          >
            <View style={styles.menuItemLeft}>
              {item.icon}
              <Text style={styles.menuItemText}>{item.title}</Text>
            </View>
            <ChevronRight size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.versionContainer}>
        <Text style={styles.versionText}>TaskhubSA v1.0.0</Text>
      </View>

      <TouchableOpacity
        style={[styles.logoutButton, styles.outlineButton]}
        onPress={handleLogout}
      >
        <LogOut size={18} color={colors.error} />
        <Text style={[styles.outlineButtonText, { color: colors.error }]}>
          {t("logout")}
        </Text>
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    padding: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  profileInfo: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    marginRight: 16,
  },
  placeholderAvatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    ...typography.h3,
    marginBottom: 4,
  },
  userEmail: {
    ...typography.bodySmall,
    color: colors.textSecondary,
    marginBottom: 2,
  },
  userLocation: {
    ...typography.bodySmall,
    color: colors.primary,
    fontWeight: "500",
  },
  menuSection: {
    backgroundColor: colors.white,
    marginTop: 16,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: colors.border,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  menuItemLeft: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  menuItemText: {
    ...typography.body,
  },
  versionContainer: {
    padding: 16,
    alignItems: "center",
  },
  versionText: {
    ...typography.caption,
    color: colors.textSecondary,
  },
  logoutButton: {
    marginHorizontal: 16,
    marginBottom: 32,
    borderColor: colors.error,
  },
  authContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
  },
  authTitle: {
    ...typography.h2,
    marginTop: 16,
    marginBottom: 8,
    textAlign: "center",
  },
  authMessage: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: 32,
  },
  authButton: {
    minWidth: 200,
    marginBottom: 16,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  editButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: "center",
    justifyContent: "center",
  },
  primaryButton: {
    backgroundColor: colors.primary,
  },
  outlineButton: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: colors.primary,
    flexDirection: "row",
    gap: 8,
  },
  buttonText: {
    ...typography.button,
    color: colors.white,
    fontWeight: "600",
  },
  outlineButtonText: {
    ...typography.button,
    color: colors.primary,
    fontWeight: "600",
  },
  registerText: {
    ...typography.body,
    color: colors.textSecondary,
  },
  registerLink: {
    color: colors.primary,
    fontWeight: "600",
  },
});