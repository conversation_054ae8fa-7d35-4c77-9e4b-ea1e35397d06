import React from "react";
import { StyleSheet, Text, View, Image } from "react-native";
import { Review } from "@/mocks/reviews";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { Star } from "lucide-react-native";
import { useTranslation } from "@/hooks/useTranslation";

interface ReviewCardProps {
  review: Review;
}

export default function ReviewCard({ review }: ReviewCardProps) {
  const { t } = useTranslation();
  
  // Format date to readable format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <View style={styles.card}>
      <View style={styles.header}>
        <Image source={{ uri: review.userAvatar }} style={styles.avatar} />
        <View style={styles.userInfo}>
          <Text style={styles.userName}>{t(review.userNameKey)}</Text>
          <Text style={styles.date}>{formatDate(review.date)}</Text>
        </View>
        <View style={styles.ratingContainer}>
          <Star
            size={16}
            color={colors.secondary}
            fill={colors.secondary}
            strokeWidth={2}
          />
          <Text style={styles.rating}>{review.rating}</Text>
        </View>
      </View>
      <Text style={styles.serviceName}>{t(review.serviceNameKey)}</Text>
      <Text style={styles.comment}>{t(review.commentKey)}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  header: {
    flexDirection: "row",
    marginBottom: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  userInfo: {
    flex: 1,
    justifyContent: "center",
  },
  userName: {
    ...typography.body,
    fontWeight: "700",
  },
  date: {
    ...typography.caption,
    color: colors.textSecondary,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  rating: {
    ...typography.bodySmall,
    fontWeight: "600",
  },
  serviceName: {
    ...typography.bodySmall,
    color: colors.primary,
    marginBottom: 8,
  },
  comment: {
    ...typography.body,
  },
});