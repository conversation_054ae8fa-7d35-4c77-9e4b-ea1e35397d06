import React from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity } from "react-native";
import { Stack } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { ChevronDown, ChevronUp, MessageCircle, Phone, Mail } from "lucide-react-native";
import { useState } from "react";
import { useTranslation } from "@/hooks/useTranslation";

interface FAQItem {
  question: string;
  answer: string;
}

export default function HelpSupportScreen() {
  const { t } = useTranslation();
  const [expandedFAQ, setExpandedFAQ] = useState<number | null>(null);

  const faqs: FAQItem[] = [
    {
      question: t("faqQuestion1"),
      answer: t("faqAnswer1"),
    },
    {
      question: t("faqQuestion2"),
      answer: t("faqAnswer2"),
    },
    {
      question: t("faqQuestion3"),
      answer: t("faqAnswer3"),
    },
    {
      question: t("faqQuestion4"),
      answer: t("faqAnswer4"),
    },
    {
      question: t("faqQuestion5"),
      answer: t("faqAnswer5"),
    },
  ];

  const toggleFAQ = (index: number) => {
    setExpandedFAQ(expandedFAQ === index ? null : index);
  };

  return (
    <>
      <Stack.Screen options={{ title: t("helpSupport") }} />
      
      <ScrollView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>{t("howCanWeHelp")}</Text>
          <Text style={styles.description}>{t("helpDescription")}</Text>
        </View>
        
        <View style={styles.faqSection}>
          <Text style={styles.sectionTitle}>{t("frequentlyAskedQuestions")}</Text>
          
          {faqs.map((faq, index) => (
            <View key={index} style={styles.faqItem}>
              <TouchableOpacity
                style={styles.faqQuestion}
                onPress={() => toggleFAQ(index)}
              >
                <Text style={styles.questionText}>{faq.question}</Text>
                {expandedFAQ === index ? (
                  <ChevronUp size={20} color={colors.primary} />
                ) : (
                  <ChevronDown size={20} color={colors.text} />
                )}
              </TouchableOpacity>
              
              {expandedFAQ === index && (
                <Text style={styles.answerText}>{faq.answer}</Text>
              )}
            </View>
          ))}
        </View>
        
        <View style={styles.contactSection}>
          <Text style={styles.contactTitle}>{t("stillNeedHelp")}</Text>
          
          <TouchableOpacity style={styles.contactOption}>
            <MessageCircle size={24} color={colors.primary} />
            <View style={styles.contactInfo}>
              <Text style={styles.contactLabel}>{t("chatWithUs")}</Text>
              <Text style={styles.contactDescription}>
                {t("chatSupportDescription")}
              </Text>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.contactOption}>
            <Phone size={24} color={colors.primary} />
            <View style={styles.contactInfo}>
              <Text style={styles.contactLabel}>{t("callUs")}</Text>
              <Text style={styles.contactDescription}>
                +27 21 123 4567
              </Text>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.contactOption}>
            <Mail size={24} color={colors.primary} />
            <View style={styles.contactInfo}>
              <Text style={styles.contactLabel}>{t("emailUs")}</Text>
              <Text style={styles.contactDescription}>
                <EMAIL>
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    padding: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  title: {
    ...typography.h2,
    marginBottom: 8,
  },
  description: {
    ...typography.body,
    color: colors.textSecondary,
  },
  faqSection: {
    backgroundColor: colors.white,
    marginTop: 16,
    padding: 16,
  },
  sectionTitle: {
    ...typography.h3,
    marginBottom: 16,
  },
  faqItem: {
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    paddingBottom: 16,
  },
  faqQuestion: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  questionText: {
    ...typography.body,
    fontWeight: "600",
    flex: 1,
    marginRight: 8,
  },
  answerText: {
    ...typography.body,
    color: colors.textSecondary,
    marginTop: 8,
  },
  contactSection: {
    backgroundColor: colors.white,
    marginTop: 16,
    padding: 16,
    marginBottom: 32,
  },
  contactTitle: {
    ...typography.h3,
    marginBottom: 16,
  },
  contactOption: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  contactInfo: {
    marginLeft: 16,
    flex: 1,
  },
  contactLabel: {
    ...typography.body,
    fontWeight: "600",
    marginBottom: 4,
  },
  contactDescription: {
    ...typography.bodySmall,
    color: colors.textSecondary,
  },
});