import { useCallback } from "react";
import { useLanguageStore } from "@/stores/useLanguageStore";
import translations from "@/constants/translations";

type TranslationParams = Record<string, string | number>;

export const useTranslation = () => {
  const { language } = useLanguageStore();
  
  // Get the translations for the current language or fall back to English
  const currentTranslations = translations[language as keyof typeof translations] || translations.en;
  
  // Translation function that supports parameters
  const t = useCallback((key: string, params?: TranslationParams): string => {
    // Get the translation string
    const translationString = currentTranslations[key as keyof typeof currentTranslations] || key;
    
    // If no parameters, return the translation string
    if (!params) {
      return translationString;
    }
    
    // Replace parameters in the translation string
    let result = translationString;
    Object.entries(params).forEach(([paramKey, paramValue]) => {
      result = result.replace(`{${paramKey}}`, String(paramValue));
    });
    
    return result;
  }, [language, currentTranslations]);
  
  return { t };
};