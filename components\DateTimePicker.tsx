import React, { useState } from "react";
import { StyleSheet, Text, View, TouchableOpacity, ScrollView } from "react-native";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { Calendar, Clock } from "lucide-react-native";

interface DateTimePickerProps {
  onSelectDateTime: (date: string, time: string) => void;
}

export default function DateTimePicker({ onSelectDateTime }: DateTimePickerProps) {
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [selectedTime, setSelectedTime] = useState<string | null>(null);

  // Generate next 7 days
  const getDates = () => {
    const dates = [];
    const today = new Date();
    
    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      
      const formattedDate = date.toLocaleDateString("en-ZA", {
        weekday: "short",
        day: "numeric",
        month: "short",
      });
      
      dates.push({
        value: date.toISOString().split("T")[0],
        label: formattedDate,
      });
    }
    
    return dates;
  };

  // Generate time slots from 8AM to 6PM
  const getTimes = () => {
    const times = [];
    const startHour = 8;
    const endHour = 18;
    
    for (let hour = startHour; hour <= endHour; hour++) {
      const formattedHour = hour % 12 === 0 ? 12 : hour % 12;
      const period = hour < 12 ? "AM" : "PM";
      
      times.push({
        value: `${hour}:00`,
        label: `${formattedHour}:00 ${period}`,
      });
      
      if (hour < endHour) {
        times.push({
          value: `${hour}:30`,
          label: `${formattedHour}:30 ${period}`,
        });
      }
    }
    
    return times;
  };

  const dates = getDates();
  const times = getTimes();

  const handleDateSelect = (date: string) => {
    setSelectedDate(date);
    if (selectedTime) {
      onSelectDateTime(date, selectedTime);
    }
  };

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
    if (selectedDate) {
      onSelectDateTime(selectedDate, time);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Calendar size={18} color={colors.primary} />
          <Text style={styles.sectionTitle}>Select Date</Text>
        </View>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.dateContainer}>
            {dates.map((date) => (
              <TouchableOpacity
                key={date.value}
                style={[
                  styles.dateButton,
                  selectedDate === date.value && styles.selectedDateButton,
                ]}
                onPress={() => handleDateSelect(date.value)}
              >
                <Text
                  style={[
                    styles.dateText,
                    selectedDate === date.value && styles.selectedDateText,
                  ]}
                >
                  {date.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Clock size={18} color={colors.primary} />
          <Text style={styles.sectionTitle}>Select Time</Text>
        </View>
        <View style={styles.timeContainer}>
          {times.map((time) => (
            <TouchableOpacity
              key={time.value}
              style={[
                styles.timeButton,
                selectedTime === time.value && styles.selectedTimeButton,
              ]}
              onPress={() => handleTimeSelect(time.value)}
            >
              <Text
                style={[
                  styles.timeText,
                  selectedTime === time.value && styles.selectedTimeText,
                ]}
              >
                {time.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    gap: 16,
  },
  section: {
    gap: 12,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  sectionTitle: {
    ...typography.h4,
    color: colors.primary,
  },
  dateContainer: {
    flexDirection: "row",
    gap: 8,
  },
  dateButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: colors.card,
    borderWidth: 1,
    borderColor: colors.border,
    minWidth: 100,
    alignItems: "center",
  },
  selectedDateButton: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  dateText: {
    ...typography.body,
  },
  selectedDateText: {
    color: colors.white,
    fontWeight: "600",
  },
  timeContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  timeButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: colors.card,
    borderWidth: 1,
    borderColor: colors.border,
  },
  selectedTimeButton: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  timeText: {
    ...typography.bodySmall,
  },
  selectedTimeText: {
    color: colors.white,
    fontWeight: "600",
  },
});