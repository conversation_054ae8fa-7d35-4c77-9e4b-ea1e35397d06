import React from "react";
import { StyleSheet, Text, View } from "react-native";
import colors from "@/constants/colors";
import typography from "@/constants/typography";

export default function HomeScreen() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>TaskHub SA</Text>
      <Text style={styles.subtitle}>Service Marketplace</Text>
      <Text style={styles.message}>Welcome! Your app is working!</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: "bold",
    color: colors.primary,
    marginBottom: 8,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 18,
    color: colors.text,
    marginBottom: 16,
    textAlign: "center",
  },
  message: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: "center",
  },
});