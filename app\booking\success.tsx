import React from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity } from "react-native";
import { useLocalSearchParams, useRouter, Stack } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { useBookingStore } from "@/stores/useBookingStore";
import { CheckCircle, Calendar, Clock, MapPin } from "lucide-react-native";

export default function BookingSuccessScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { getBookingById } = useBookingStore();

  const booking = getBookingById(id as string);

  const handleViewBookings = () => {
    router.push("/bookings");
  };

  const handleBackToHome = () => {
    router.push("/");
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "";
    const options: Intl.DateTimeFormatOptions = {
      weekday: "long",
      day: "numeric",
      month: "long",
      year: "numeric",
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return "";
    const [hours, minutes] = timeString.split(":");
    const hour = parseInt(hours, 10);
    const period = hour >= 12 ? "PM" : "AM";
    const formattedHour = hour % 12 === 0 ? 12 : hour % 12;
    return `${formattedHour}:${minutes} ${period}`;
  };

  return (
    <>
      <Stack.Screen options={{ title: "Booking Confirmed", headerBackVisible: false }} />

      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <View style={styles.successIcon}>
          <CheckCircle size={64} color={colors.success} strokeWidth={2} />
        </View>

        <Text style={styles.title}>Booking Confirmed!</Text>
        <Text style={styles.message}>
          Your booking has been successfully confirmed. The service provider will be notified.
        </Text>

        {booking && (
          <View style={styles.bookingDetails}>
            <Text style={styles.detailsTitle}>Booking Details</Text>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Provider</Text>
              <Text style={styles.detailValue}>{booking.providerName}</Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Service</Text>
              <Text style={styles.detailValue}>{booking.serviceName}</Text>
            </View>

            <View style={styles.detailRow}>
              <View style={styles.iconTextContainer}>
                <Calendar size={16} color={colors.primary} />
                <Text style={styles.iconText}>{formatDate(booking.date)}</Text>
              </View>
            </View>

            <View style={styles.detailRow}>
              <View style={styles.iconTextContainer}>
                <Clock size={16} color={colors.primary} />
                <Text style={styles.iconText}>{formatTime(booking.time)}</Text>
              </View>
            </View>

            <View style={styles.priceContainer}>
              <Text style={styles.priceLabel}>Total</Text>
              <Text style={styles.price}>R{booking.price}</Text>
            </View>
          </View>
        )}

        <View style={styles.instructionsContainer}>
          <Text style={styles.instructionsTitle}>What's Next?</Text>
          <Text style={styles.instructionsText}>
            1. The service provider will contact you to confirm the booking.
          </Text>
          <Text style={styles.instructionsText}>
            2. Prepare for your appointment by ensuring access to your location.
          </Text>
          <Text style={styles.instructionsText}>
            3. Payment will be made directly to the service provider after the service is completed.
          </Text>
        </View>

        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={[styles.viewBookingsButton, styles.primaryButton]}
            onPress={handleViewBookings}
          >
            <Text style={styles.buttonText}>View My Bookings</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.homeButton, styles.outlineButton]}
            onPress={handleBackToHome}
          >
            <Text style={styles.outlineButtonText}>Back to Home</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  contentContainer: {
    padding: 16,
    alignItems: "center",
  },
  successIcon: {
    marginTop: 32,
    marginBottom: 24,
    padding: 16,
    borderRadius: 50,
    backgroundColor: colors.success + "15",
  },
  title: {
    ...typography.h1,
    marginBottom: 16,
    textAlign: "center",
    color: colors.primary,
  },
  message: {
    ...typography.body,
    textAlign: "center",
    marginBottom: 32,
    paddingHorizontal: 16,
    color: colors.textSecondary,
  },
  bookingDetails: {
    width: "100%",
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  detailsTitle: {
    ...typography.h3,
    marginBottom: 16,
    color: colors.primary,
  },
  detailRow: {
    marginBottom: 12,
  },
  detailLabel: {
    ...typography.bodySmall,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  detailValue: {
    ...typography.body,
    fontWeight: "500",
  },
  iconTextContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  iconText: {
    ...typography.body,
  },
  priceContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  priceLabel: {
    ...typography.body,
    fontWeight: "600",
  },
  price: {
    ...typography.h3,
    color: colors.primary,
  },
  instructionsContainer: {
    width: "100%",
    backgroundColor: colors.primaryLight,
    borderRadius: 12,
    padding: 16,
    marginBottom: 32,
  },
  instructionsTitle: {
    ...typography.h4,
    marginBottom: 12,
    color: colors.primary,
  },
  instructionsText: {
    ...typography.body,
    marginBottom: 8,
  },
  buttonsContainer: {
    width: "100%",
    gap: 12,
    marginBottom: 32,
  },
  viewBookingsButton: {
    width: "100%",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  homeButton: {
    width: "100%",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  primaryButton: {
    backgroundColor: colors.primary,
  },
  outlineButton: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: colors.primary,
  },
  buttonText: {
    ...typography.button,
    color: colors.white,
    fontWeight: "600",
  },
  outlineButtonText: {
    ...typography.button,
    color: colors.primary,
    fontWeight: "600",
  },
});