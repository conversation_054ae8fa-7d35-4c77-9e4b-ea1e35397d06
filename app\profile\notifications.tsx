import React, { useState } from "react";
import { StyleSheet, Text, View, ScrollView, Switch } from "react-native";
import { Stack } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import Button from "@/components/Button";
import { useTranslation } from "@/hooks/useTranslation";

export default function NotificationsScreen() {
  const { t } = useTranslation();
  const [notifications, setNotifications] = useState({
    bookingUpdates: true,
    messages: true,
    promotions: false,
    serviceReminders: true,
    recommendations: false,
  });

  const toggleNotification = (key: keyof typeof notifications) => {
    setNotifications((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const unsubscribeAll = () => {
    setNotifications({
      bookingUpdates: false,
      messages: false,
      promotions: false,
      serviceReminders: false,
      recommendations: false,
    });
  };

  return (
    <>
      <Stack.Screen options={{ title: t("notifications") }} />
      
      <ScrollView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>{t("notificationPreferences")}</Text>
          <Text style={styles.description}>
            {t("notificationPreferencesDescription")}
          </Text>
        </View>
        
        <View style={styles.notificationsList}>
          <View style={styles.notificationItem}>
            <View style={styles.notificationInfo}>
              <Text style={styles.notificationTitle}>{t("bookingUpdates")}</Text>
              <Text style={styles.notificationDescription}>
                {t("bookingUpdatesDescription")}
              </Text>
            </View>
            <Switch
              value={notifications.bookingUpdates}
              onValueChange={() => toggleNotification("bookingUpdates")}
              trackColor={{ false: colors.border, true: colors.primaryLight }}
              thumbColor={notifications.bookingUpdates ? colors.primary : colors.card}
            />
          </View>
          
          <View style={styles.notificationItem}>
            <View style={styles.notificationInfo}>
              <Text style={styles.notificationTitle}>{t("messagesNotifications")}</Text>
              <Text style={styles.notificationDescription}>
                {t("messagesDescription")}
              </Text>
            </View>
            <Switch
              value={notifications.messages}
              onValueChange={() => toggleNotification("messages")}
              trackColor={{ false: colors.border, true: colors.primaryLight }}
              thumbColor={notifications.messages ? colors.primary : colors.card}
            />
          </View>
          
          <View style={styles.notificationItem}>
            <View style={styles.notificationInfo}>
              <Text style={styles.notificationTitle}>{t("promotions")}</Text>
              <Text style={styles.notificationDescription}>
                {t("promotionsDescription")}
              </Text>
            </View>
            <Switch
              value={notifications.promotions}
              onValueChange={() => toggleNotification("promotions")}
              trackColor={{ false: colors.border, true: colors.primaryLight }}
              thumbColor={notifications.promotions ? colors.primary : colors.card}
            />
          </View>
          
          <View style={styles.notificationItem}>
            <View style={styles.notificationInfo}>
              <Text style={styles.notificationTitle}>{t("serviceReminders")}</Text>
              <Text style={styles.notificationDescription}>
                {t("serviceRemindersDescription")}
              </Text>
            </View>
            <Switch
              value={notifications.serviceReminders}
              onValueChange={() => toggleNotification("serviceReminders")}
              trackColor={{ false: colors.border, true: colors.primaryLight }}
              thumbColor={notifications.serviceReminders ? colors.primary : colors.card}
            />
          </View>
          
          <View style={styles.notificationItem}>
            <View style={styles.notificationInfo}>
              <Text style={styles.notificationTitle}>{t("recommendations")}</Text>
              <Text style={styles.notificationDescription}>
                {t("recommendationsDescription")}
              </Text>
            </View>
            <Switch
              value={notifications.recommendations}
              onValueChange={() => toggleNotification("recommendations")}
              trackColor={{ false: colors.border, true: colors.primaryLight }}
              thumbColor={notifications.recommendations ? colors.primary : colors.card}
            />
          </View>
        </View>
        
        <Button
          title={t("unsubscribeAll")}
          onPress={unsubscribeAll}
          variant="outline"
          style={styles.unsubscribeButton}
        />
        
        <Text style={styles.note}>{t("notificationNote")}</Text>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    padding: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  title: {
    ...typography.h2,
    marginBottom: 8,
  },
  description: {
    ...typography.body,
    color: colors.textSecondary,
  },
  notificationsList: {
    backgroundColor: colors.white,
    marginTop: 16,
  },
  notificationItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  notificationInfo: {
    flex: 1,
    marginRight: 16,
  },
  notificationTitle: {
    ...typography.body,
    fontWeight: "600",
    marginBottom: 4,
  },
  notificationDescription: {
    ...typography.bodySmall,
    color: colors.textSecondary,
  },
  unsubscribeButton: {
    margin: 16,
  },
  note: {
    ...typography.bodySmall,
    color: colors.textSecondary,
    textAlign: "center",
    marginHorizontal: 16,
    marginBottom: 32,
  },
});