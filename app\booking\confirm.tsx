import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, TextInput, Alert, TouchableOpacity } from "react-native";
import { useRouter, Stack, useLocalSearchParams } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";

import { useBookingStore } from "@/stores/useBookingStore";
import { Calendar, Clock, AlertCircle } from "lucide-react-native";
import { useTranslation } from "@/hooks/useTranslation";
import PaymentMethodSelector from "@/components/PaymentMethodSelector";
import CardDetailsForm from "@/components/CardDetailsForm";
import { serviceProviders } from "@/mocks/providers";
import DateTimePicker from "@/components/DateTimePicker";

export default function BookingConfirmScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const { providerId, serviceId } = params;
  const { t } = useTranslation();

  const {
    currentBooking,
    createBooking,
    isLoading,
    error,
    setCurrentBookingProvider,
    setCurrentBookingService,
    setCurrentBookingDateTime,
    setCurrentBookingNotes
  } = useBookingStore();

  const [paymentMethod, setPaymentMethod] = useState<"card" | "cash">("cash");
  const [isCardValid, setIsCardValid] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [notes, setNotes] = useState("");

  // Initialize booking data from URL params
  useEffect(() => {
    if (providerId && serviceId) {
      // Find provider and service from the mocks
      const provider = serviceProviders.find(p => p.id === providerId);
      if (provider) {
        // Set provider info
        setCurrentBookingProvider(provider.id, provider.nameKey);

        // Find and set service info
        const service = provider.services.find(s => s.id === serviceId);
        if (service) {
          setCurrentBookingService(service);
        }
      }
    }
  }, [providerId, serviceId, setCurrentBookingProvider, setCurrentBookingService]);

  // Update notes in the store when they change
  useEffect(() => {
    if (notes) {
      setCurrentBookingNotes(notes);
    }
  }, [notes, setCurrentBookingNotes]);

  const handleDateTimeSelect = (date: string, time: string) => {
    setCurrentBookingDateTime(date, time);
  };

  if (!currentBooking || !currentBooking.service) {
    return (
      <View style={styles.errorContainer}>
        <AlertCircle size={48} color={colors.error} />
        <Text style={styles.errorTitle}>Booking information missing</Text>
        <Text style={styles.errorMessage}>
          Please select a service and date/time before confirming your booking.
        </Text>
        <TouchableOpacity
          style={[styles.errorButton, styles.primaryButton]}
          onPress={() => router.back()}
        >
          <Text style={styles.buttonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const handleConfirmBooking = async () => {
    if (paymentMethod === "card" && !isCardValid) {
      Alert.alert("Invalid Card Details", "Please check your card information and try again.");
      return;
    }

    // Validate date and time are selected
    if (!currentBooking.date || !currentBooking.time) {
      Alert.alert("Missing Information", "Please select a date and time for your booking.");
      return;
    }

    // Simulate payment processing for card payments
    if (paymentMethod === "card") {
      setIsProcessing(true);

      // Simulate payment processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      setIsProcessing(false);
    }

    const booking = await createBooking();
    if (booking) {
      router.push({
        pathname: "/booking/success",
        params: { id: booking.id },
      });
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "Select a date";

    const options: Intl.DateTimeFormatOptions = {
      weekday: "long",
      day: "numeric",
      month: "long",
      year: "numeric",
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return "Select a time";

    const [hours, minutes] = timeString.split(":");
    const hour = parseInt(hours, 10);
    const period = hour >= 12 ? "PM" : "AM";
    const formattedHour = hour % 12 === 0 ? 12 : hour % 12;
    return `${formattedHour}:${minutes} ${period}`;
  };

  return (
    <>
      <Stack.Screen options={{ title: "Confirm Booking" }} />

      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Booking Summary</Text>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Provider</Text>
            <Text style={styles.detailValue}>{currentBooking.providerName}</Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Service</Text>
            <Text style={styles.detailValue}>{t(currentBooking.service.nameKey)}</Text>
          </View>

          <View style={styles.detailRow}>
            <View style={styles.dateTimeContainer}>
              <Calendar size={16} color={colors.primary} />
              <Text style={styles.dateTimeText}>{formatDate(currentBooking.date)}</Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <View style={styles.dateTimeContainer}>
              <Clock size={16} color={colors.primary} />
              <Text style={styles.dateTimeText}>{formatTime(currentBooking.time)}</Text>
            </View>
          </View>

          {currentBooking.notes && (
            <View style={styles.notesContainer}>
              <Text style={styles.notesLabel}>Additional Notes</Text>
              <Text style={styles.notesText}>{currentBooking.notes}</Text>
            </View>
          )}
        </View>

        {/* Date and Time Picker */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Select Date & Time</Text>
          <DateTimePicker onSelectDateTime={handleDateTimeSelect} />
        </View>

        {/* Notes Input */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Additional Notes</Text>
          <TextInput
            style={styles.notesInput}
            placeholder="Add any special instructions or requirements..."
            multiline
            numberOfLines={4}
            value={notes}
            onChangeText={setNotes}
          />
        </View>

        <View style={styles.card}>
          <Text style={styles.cardTitle}>Price Details</Text>

          <View style={styles.priceRow}>
            <Text style={styles.priceLabel}>{t(currentBooking.service.nameKey)}</Text>
            <Text style={styles.priceValue}>R{currentBooking.service.price}</Text>
          </View>

          <View style={styles.divider} />

          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Total</Text>
            <Text style={styles.totalValue}>R{currentBooking.service.price}</Text>
          </View>
        </View>

        <PaymentMethodSelector
          selectedMethod={paymentMethod}
          onSelectMethod={setPaymentMethod}
        />

        {paymentMethod === "card" && (
          <CardDetailsForm onComplete={setIsCardValid} />
        )}

        {error && (
          <View style={styles.errorMessage}>
            <AlertCircle size={16} color={colors.error} />
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}
      </ScrollView>

      <View style={styles.footer}>
        <View style={styles.footerContent}>
          <View>
            <Text style={styles.footerLabel}>Total</Text>
            <Text style={styles.footerPrice}>R{currentBooking.service.price}</Text>
          </View>

          <TouchableOpacity
            style={[styles.confirmButton, styles.primaryButton]}
            onPress={handleConfirmBooking}
            disabled={isLoading || isProcessing || (paymentMethod === "card" && !isCardValid) || !currentBooking.date || !currentBooking.time}
          >
            <Text style={styles.buttonText}>
              {isProcessing ? "Processing Payment..." : "Confirm Booking"}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    padding: 16,
  },
  card: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  cardTitle: {
    ...typography.h3,
    marginBottom: 16,
  },
  detailRow: {
    marginBottom: 12,
  },
  detailLabel: {
    ...typography.bodySmall,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  detailValue: {
    ...typography.body,
    fontWeight: "500",
  },
  dateTimeContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  dateTimeText: {
    ...typography.body,
  },
  notesContainer: {
    marginTop: 8,
    padding: 12,
    backgroundColor: colors.card,
    borderRadius: 8,
  },
  notesLabel: {
    ...typography.bodySmall,
    fontWeight: "600",
    marginBottom: 4,
  },
  notesText: {
    ...typography.bodySmall,
  },
  notesInput: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: 12,
    ...typography.body,
    minHeight: 100,
    textAlignVertical: "top",
  },
  priceRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  priceLabel: {
    ...typography.body,
  },
  priceValue: {
    ...typography.body,
  },
  divider: {
    height: 1,
    backgroundColor: colors.border,
    marginVertical: 12,
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  totalLabel: {
    ...typography.body,
    fontWeight: "600",
  },
  totalValue: {
    ...typography.h3,
    color: colors.primary,
  },
  footer: {
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    padding: 16,
  },
  footerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  footerLabel: {
    ...typography.bodySmall,
    color: colors.textSecondary,
  },
  footerPrice: {
    ...typography.h3,
    color: colors.primary,
  },
  errorContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
  },
  errorTitle: {
    ...typography.h2,
    marginTop: 16,
    marginBottom: 8,
    textAlign: "center",
  },
  errorMessage: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    padding: 12,
    backgroundColor: colors.error + "15",
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    ...typography.bodySmall,
    color: colors.error,
  },
  errorButton: {
    marginTop: 24,
    minWidth: 200,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  confirmButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    minWidth: 150,
  },
  primaryButton: {
    backgroundColor: colors.primary,
  },
  buttonText: {
    ...typography.button,
    color: colors.white,
    fontWeight: "600",
  },
});