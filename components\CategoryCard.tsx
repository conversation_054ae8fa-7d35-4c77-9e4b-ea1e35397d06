import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { ServiceCategory } from "@/mocks/categories";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import * as Icons from "lucide-react-native";
import { useTranslation } from "@/hooks/useTranslation";

interface CategoryCardProps {
  category: ServiceCategory;
  onPress: (category: ServiceCategory) => void;
}

export default function CategoryCard({ category, onPress }: CategoryCardProps) {
  const { t } = useTranslation();
  
  // Dynamically get the icon component
  const IconComponent = (Icons as any)[
    category.icon.charAt(0).toUpperCase() + category.icon.slice(1)
  ];

  return (
    <TouchableOpacity
      style={styles.card}
      onPress={() => onPress(category)}
      activeOpacity={0.7}
    >
      <View style={styles.iconContainer}>
        {IconComponent && (
          <IconComponent size={24} color={colors.primary} strokeWidth={2} />
        )}
      </View>
      <Text style={styles.name} numberOfLines={1}>
        {t(category.nameKey)}
      </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    width: 100,
    alignItems: "center",
    marginHorizontal: 8,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: colors.primaryLight,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  name: {
    ...typography.bodySmall,
    textAlign: "center",
    fontWeight: "500",
  },
});