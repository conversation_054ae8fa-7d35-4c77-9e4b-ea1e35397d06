import React, { useState, useEffect, useRef } from "react";
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  Image,
  ActivityIndicator,
} from "react-native";
import { Stack, useLocalSearchParams, useRouter } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { useTranslation } from "@/hooks/useTranslation";
import { Send, Image as ImageIcon, Paperclip, Mic } from "lucide-react-native";
import { serviceProviders } from "@/mocks/providers";
import { useAuthStore } from "@/stores/useAuthStore";

interface Message {
  id: string;
  text: string;
  sender: "user" | "provider";
  timestamp: Date;
  status: "sent" | "delivered" | "read";
}

export default function ChatScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { t } = useTranslation();
  const { user } = useAuthStore();
  
  const flatListRef = useRef<FlatList>(null);
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  
  // Find provider by ID
  const provider = serviceProviders.find(p => p.id === id);
  
  useEffect(() => {
    if (!provider) {
      setLoading(false);
      return;
    }
    
    // Load initial messages
    const initialMessages: Message[] = [
      {
        id: "1",
        text: t("providerInitialMessage", { name: provider.nameKey ? t(provider.nameKey) : "" }),
        sender: "provider",
        timestamp: new Date(Date.now() - 3600000), // 1 hour ago
        status: "read",
      },
      {
        id: "2",
        text: t("userInitialMessage"),
        sender: "user",
        timestamp: new Date(Date.now() - 3000000), // 50 minutes ago
        status: "read",
      },
      {
        id: "3",
        text: t("providerFollowupMessage"),
        sender: "provider",
        timestamp: new Date(Date.now() - 2400000), // 40 minutes ago
        status: "read",
      },
    ];
    
    // Simulate loading
    setTimeout(() => {
      setMessages(initialMessages);
      setLoading(false);
    }, 500);
  }, [id, t, provider]);
  
  const sendMessage = () => {
    if (!message.trim()) return;
    
    const newMessage: Message = {
      id: Date.now().toString(),
      text: message,
      sender: "user",
      timestamp: new Date(),
      status: "sent",
    };
    
    setMessages(prev => [...prev, newMessage]);
    setMessage("");
    
    // Simulate provider response after a delay
    setTimeout(() => {
      const responseMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: t("providerAutoResponse"),
        sender: "provider",
        timestamp: new Date(),
        status: "sent",
      };
      
      setMessages(prev => [...prev, responseMessage]);
    }, 2000);
  };
  
  useEffect(() => {
    // Scroll to bottom when messages change
    if (flatListRef.current && messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  if (!provider) {
    return (
      <View style={styles.notFound}>
        <Text style={styles.notFoundText}>{t("chatNotFound")}</Text>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen 
        options={{ 
          title: t(provider.nameKey),
          headerRight: () => (
            <TouchableOpacity onPress={() => router.push({
              pathname: "/provider/[id]",
              params: { id: provider.id },
            })}>
              <Image source={{ uri: provider.avatar }} style={styles.headerAvatar} />
            </TouchableOpacity>
          ),
        }} 
      />
      
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 90 : 0}
      >
        <FlatList
          ref={flatListRef}
          data={messages}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.messageList}
          renderItem={({ item }) => (
            <View
              style={[
                styles.messageBubble,
                item.sender === "user" ? styles.userBubble : styles.providerBubble,
              ]}
            >
              <Text style={[
                styles.messageText,
                item.sender === "user" ? styles.userMessageText : styles.providerMessageText,
              ]}>
                {item.text}
              </Text>
              <Text style={[
                styles.messageTime,
                item.sender === "user" ? styles.userMessageTime : styles.providerMessageTime,
              ]}>
                {item.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
              </Text>
            </View>
          )}
        />
        
        <View style={styles.inputContainer}>
          <TouchableOpacity style={styles.attachButton}>
            <Paperclip size={20} color={colors.textSecondary} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.attachButton}>
            <ImageIcon size={20} color={colors.textSecondary} />
          </TouchableOpacity>
          
          <TextInput
            style={styles.input}
            value={message}
            onChangeText={setMessage}
            placeholder={t("typeMessage")}
            multiline
          />
          
          {message.trim() ? (
            <TouchableOpacity style={styles.sendButton} onPress={sendMessage}>
              <Send size={20} color={colors.white} />
            </TouchableOpacity>
          ) : (
            <TouchableOpacity style={styles.micButton}>
              <Mic size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
      </KeyboardAvoidingView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.background,
  },
  headerAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  messageList: {
    padding: 16,
  },
  messageBubble: {
    maxWidth: "80%",
    padding: 12,
    borderRadius: 16,
    marginBottom: 8,
  },
  userBubble: {
    backgroundColor: colors.primary,
    alignSelf: "flex-end",
    borderBottomRightRadius: 4,
  },
  providerBubble: {
    backgroundColor: colors.card,
    alignSelf: "flex-start",
    borderBottomLeftRadius: 4,
  },
  messageText: {
    ...typography.body,
  },
  userMessageText: {
    color: colors.white,
  },
  providerMessageText: {
    color: colors.text,
  },
  messageTime: {
    ...typography.caption,
    alignSelf: "flex-end",
    marginTop: 4,
  },
  userMessageTime: {
    color: colors.white + "CC",
  },
  providerMessageTime: {
    color: colors.textSecondary,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 8,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  attachButton: {
    padding: 8,
  },
  input: {
    flex: 1,
    backgroundColor: colors.card,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    maxHeight: 100,
    ...typography.body,
  },
  sendButton: {
    backgroundColor: colors.primary,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 8,
  },
  micButton: {
    padding: 8,
    marginLeft: 8,
  },
  notFound: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
  },
  notFoundText: {
    ...typography.h2,
    marginBottom: 16,
  },
});